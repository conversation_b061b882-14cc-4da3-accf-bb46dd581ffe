# -*- coding: utf-8 -*-
import sqlite3
import os
from datetime import datetime, timedelta
import json
from database import ExcelDataDB

class DataManager:
    def __init__(self):
        self.db = ExcelDataDB()
    
    def create_upload_record(self, file_name, file_path, file_size=0, description=""):
        """创建上传记录"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO upload_records 
                (file_name, file_path, file_size, description) 
                VALUES (?, ?, ?, ?)
            ''', (file_name, file_path, file_size, description))
            
            upload_id = cursor.lastrowid
            conn.commit()
            print(f"创建上传记录成功，ID: {upload_id}")
            return upload_id
            
        except Exception as e:
            conn.rollback()
            print(f"创建上传记录失败: {e}")
            return None
        finally:
            conn.close()
    
    def insert_work_orders(self, upload_id, excel_data):
        """批量插入工单数据"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            valid_count = 0
            total_count = len(excel_data)
            
            # 准备插入SQL
            insert_sql = '''
                INSERT INTO work_orders (
                    upload_id, row_number,
                    column_a, column_b, column_c, column_d, column_e, column_f, column_g, column_h, column_i,
                    column_j, column_k, column_l, column_m, column_n, column_o, column_p, column_q,
                    planning_big_network, planning_non_big_network,
                    construction_property, construction_transmission, construction_power, construction_tower, construction_equipment,
                    maintenance_sporadic, maintenance_outsourced,
                    optimization_antenna, optimization_backend,
                    customer_field_test, customer_communication,
                    archive_duration, is_valid
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            for row_idx, row_data in enumerate(excel_data[2:], start=3):  # 从第3行开始
                if len(row_data) < 30:  # 确保有足够的列
                    continue
                
                # 解析日期和计算归档时长
                j_date = self.parse_date(row_data[9]) if len(row_data) > 9 else None  # J列
                k_date = self.parse_date(row_data[10]) if len(row_data) > 10 else None  # K列
                archive_duration = self.calculate_archive_duration(j_date, k_date, row_data[9])
                
                # 解析经纬度
                longitude = self.parse_float(row_data[12]) if len(row_data) > 12 else None  # M列
                latitude = self.parse_float(row_data[13]) if len(row_data) > 13 else None   # N列
                
                # 验证数据有效性
                is_valid = self.validate_row_data(row_data, longitude, latitude)
                if is_valid:
                    valid_count += 1
                
                # 准备数据
                data_tuple = (
                    upload_id, row_idx,
                    # A-Q列
                    self.safe_get(row_data, 0), self.safe_get(row_data, 1), self.safe_get(row_data, 2),
                    self.safe_get(row_data, 3), self.safe_get(row_data, 4), self.safe_get(row_data, 5),
                    self.safe_get(row_data, 6), self.safe_get(row_data, 7), self.safe_get(row_data, 8),
                    j_date, k_date, self.safe_get(row_data, 11),
                    longitude, latitude, self.safe_get(row_data, 14),
                    self.safe_get(row_data, 15), self.safe_get(row_data, 16),
                    # R-S列 (规划)
                    self.safe_get(row_data, 17), self.safe_get(row_data, 18),
                    # T-X列 (建设)
                    self.safe_get(row_data, 19), self.safe_get(row_data, 20), self.safe_get(row_data, 21),
                    self.safe_get(row_data, 22), self.safe_get(row_data, 23),
                    # Y-Z列 (维护)
                    self.safe_get(row_data, 24), self.safe_get(row_data, 25),
                    # AA-AB列 (优化)
                    self.safe_get(row_data, 26), self.safe_get(row_data, 27),
                    # AC-AD列 (客户)
                    self.safe_get(row_data, 28), self.safe_get(row_data, 29),
                    # 计算字段
                    archive_duration, is_valid
                )
                
                cursor.execute(insert_sql, data_tuple)
            
            # 更新上传记录统计
            cursor.execute('''
                UPDATE upload_records 
                SET total_rows = ?, valid_rows = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (total_count, valid_count, upload_id))
            
            conn.commit()
            print(f"批量插入完成: 总计{total_count}行，有效{valid_count}行")
            return valid_count
            
        except Exception as e:
            conn.rollback()
            print(f"批量插入失败: {e}")
            return 0
        finally:
            conn.close()
    
    def get_upload_records(self, status='active'):
        """获取上传记录列表"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT id, file_name, upload_time, total_rows, valid_rows, status, description
                FROM upload_records 
                WHERE status = ? 
                ORDER BY upload_time DESC
            ''', (status,))
            
            records = cursor.fetchall()
            return [dict(record) for record in records]
            
        except Exception as e:
            print(f"获取上传记录失败: {e}")
            return []
        finally:
            conn.close()
    
    def delete_upload_data(self, upload_id):
        """删除上传数据（软删除）"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # 软删除上传记录
            cursor.execute('''
                UPDATE upload_records 
                SET status = 'deleted', updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (upload_id,))
            
            # 删除相关工单数据
            cursor.execute('DELETE FROM work_orders WHERE upload_id = ?', (upload_id,))
            
            # 清除相关缓存
            cursor.execute('DELETE FROM analysis_cache')
            
            conn.commit()
            print(f"删除上传数据成功: {upload_id}")
            return True
            
        except Exception as e:
            conn.rollback()
            print(f"删除上传数据失败: {e}")
            return False
        finally:
            conn.close()
    
    def get_all_work_orders(self):
        """获取所有工单数据（包括已归档和未归档）"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT wo.*, ur.file_name
                FROM work_orders wo
                JOIN upload_records ur ON wo.upload_id = ur.id
                WHERE ur.status = 'active'
                ORDER BY wo.row_number
            ''')

            records = cursor.fetchall()
            return [dict(record) for record in records]

        except Exception as e:
            print(f"获取工单数据失败: {e}")
            return []
        finally:
            conn.close()

    def get_archived_work_orders(self):
        """获取已归档的工单数据"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 使用O列(column_o)来判断归档状态
            cursor.execute('''
                SELECT wo.*, ur.file_name
                FROM work_orders wo
                JOIN upload_records ur ON wo.upload_id = ur.id
                WHERE ur.status = 'active'
                AND (wo.column_o LIKE '%归档%'
                     OR wo.column_o = '已归档'
                     OR wo.column_o = '归档')
                ORDER BY wo.row_number
            ''')

            records = cursor.fetchall()
            print(f"从数据库获取到 {len(records)} 条已归档数据")
            return [dict(record) for record in records]

        except Exception as e:
            print(f"获取已归档工单数据失败: {e}")
            return []
        finally:
            conn.close()

    def get_work_orders_by_status(self, status_pattern):
        """根据状态模式获取工单数据"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT wo.*, ur.file_name
                FROM work_orders wo
                JOIN upload_records ur ON wo.upload_id = ur.id
                WHERE ur.status = 'active'
                AND wo.column_q LIKE ?
                ORDER BY wo.row_number
            ''', (f'%{status_pattern}%',))

            records = cursor.fetchall()
            return [dict(record) for record in records]

        except Exception as e:
            print(f"获取工单数据失败: {e}")
            return []
        finally:
            conn.close()
    
    def get_statistics(self):
        """获取数据统计信息"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # 总上传次数
            cursor.execute("SELECT COUNT(*) as total_uploads FROM upload_records WHERE status = 'active'")
            total_uploads = cursor.fetchone()['total_uploads']
            
            # 总工单数
            cursor.execute("SELECT COUNT(*) as total_orders FROM work_orders WHERE is_valid = 1")
            total_orders = cursor.fetchone()['total_orders']
            
            # 有效数据比例
            cursor.execute("SELECT COUNT(*) as invalid_orders FROM work_orders WHERE is_valid = 0")
            invalid_orders = cursor.fetchone()['invalid_orders']
            
            # 最新上传时间
            cursor.execute("SELECT MAX(upload_time) as latest_upload FROM upload_records WHERE status = 'active'")
            latest_upload = cursor.fetchone()['latest_upload']
            
            return {
                'total_uploads': total_uploads,
                'total_orders': total_orders,
                'invalid_orders': invalid_orders,
                'valid_ratio': total_orders / (total_orders + invalid_orders) * 100 if (total_orders + invalid_orders) > 0 else 0,
                'latest_upload': latest_upload
            }
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
        finally:
            conn.close()
    
    # 辅助方法
    def safe_get(self, row_data, index):
        """安全获取列数据"""
        try:
            return str(row_data[index]) if index < len(row_data) and row_data[index] is not None else None
        except:
            return None
    
    def parse_date(self, date_value):
        """解析日期"""
        if date_value is None:
            return None
        
        try:
            if isinstance(date_value, datetime):
                return date_value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(date_value, str) and date_value.strip():
                # 尝试多种日期格式
                formats = ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']
                for fmt in formats:
                    try:
                        return datetime.strptime(date_value.strip(), fmt).strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
            elif isinstance(date_value, (int, float)):
                # Excel日期序列号
                from datetime import timedelta
                excel_epoch = datetime(1900, 1, 1)
                if date_value > 59:
                    return (excel_epoch + timedelta(days=date_value - 2)).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    return (excel_epoch + timedelta(days=date_value - 1)).strftime('%Y-%m-%d %H:%M:%S')
        except:
            pass
        
        return None
    
    def parse_float(self, value):
        """解析浮点数"""
        try:
            return float(value) if value is not None else None
        except:
            return None
    
    def calculate_archive_duration(self, j_date, k_date, j_original):
        """计算归档时长"""
        try:
            # 检查是否为"无需现场测试"
            if j_original and isinstance(j_original, str) and "无需现场测试" in str(j_original):
                return 1
            
            if j_date and k_date:
                j_dt = datetime.strptime(j_date, '%Y-%m-%d %H:%M:%S')
                k_dt = datetime.strptime(k_date, '%Y-%m-%d %H:%M:%S')
                return max(0, (k_dt - j_dt).days)
        except:
            pass
        
        return None
    
    def validate_row_data(self, row_data, longitude, latitude):
        """验证行数据有效性"""
        # 基本验证：至少要有一些关键数据
        if not row_data or len(row_data) < 10:
            return False
        
        # 经纬度验证（如果存在）
        if longitude is not None and latitude is not None:
            if not (73 <= longitude <= 135 and 18 <= latitude <= 54):
                return False
        
        return True

    def save_process_progress(self, work_order_id, selected_processes, selected_process_details,
                            current_process_index=0, process_results=None, current_page_selections=None):
        """保存流程进度"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 将列表和字典转换为JSON字符串
            selected_processes_json = json.dumps(selected_processes, ensure_ascii=False)
            selected_process_details_json = json.dumps(selected_process_details, ensure_ascii=False)
            process_results_json = json.dumps(process_results or {}, ensure_ascii=False)
            current_page_selections_json = json.dumps(current_page_selections or {}, ensure_ascii=False)

            # 检查是否已存在该工单的进度记录
            cursor.execute('''
                SELECT id FROM process_progress WHERE work_order_id = ?
            ''', (work_order_id,))

            existing_record = cursor.fetchone()

            if existing_record:
                # 更新现有记录
                cursor.execute('''
                    UPDATE process_progress
                    SET selected_processes = ?,
                        selected_process_details = ?,
                        current_process_index = ?,
                        process_results = ?,
                        current_page_selections = ?,
                        status = 'in_progress',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE work_order_id = ?
                ''', (selected_processes_json, selected_process_details_json,
                     current_process_index, process_results_json, current_page_selections_json, work_order_id))
                progress_id = existing_record['id']
            else:
                # 创建新记录
                cursor.execute('''
                    INSERT INTO process_progress
                    (work_order_id, selected_processes, selected_process_details,
                     current_process_index, process_results, current_page_selections, status)
                    VALUES (?, ?, ?, ?, ?, ?, 'in_progress')
                ''', (work_order_id, selected_processes_json, selected_process_details_json,
                     current_process_index, process_results_json, current_page_selections_json))
                progress_id = cursor.lastrowid

            conn.commit()
            print(f"保存流程进度成功，ID: {progress_id}")
            return progress_id

        except Exception as e:
            conn.rollback()
            print(f"保存流程进度失败: {e}")
            return None
        finally:
            conn.close()

    def get_process_progress(self, work_order_id):
        """获取流程进度"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 先查找进行中的流程
            cursor.execute('''
                SELECT * FROM process_progress
                WHERE work_order_id = ? AND status = 'in_progress'
                ORDER BY updated_at DESC LIMIT 1
            ''', (work_order_id,))

            record = cursor.fetchone()

            # 如果没有进行中的流程，查找已完成的流程
            if not record:
                cursor.execute('''
                    SELECT * FROM process_progress
                    WHERE work_order_id = ? AND status = 'completed'
                    ORDER BY updated_at DESC LIMIT 1
                ''', (work_order_id,))
                record = cursor.fetchone()

            if record:
                return {
                    'id': record['id'],
                    'work_order_id': record['work_order_id'],
                    'selected_processes': json.loads(record['selected_processes']),
                    'selected_process_details': json.loads(record['selected_process_details']),
                    'current_process_index': record['current_process_index'],
                    'process_results': json.loads(record['process_results']),
                    'current_page_selections': json.loads(record['current_page_selections'] or '{}'),
                    'status': record['status'],
                    'created_at': record['created_at'],
                    'updated_at': record['updated_at']
                }
            return None

        except Exception as e:
            print(f"获取流程进度失败: {e}")
            return None
        finally:
            conn.close()

    def update_process_progress(self, work_order_id, current_process_index, process_results=None, current_page_selections=None):
        """更新流程进度"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            process_results_json = json.dumps(process_results or {}, ensure_ascii=False)
            current_page_selections_json = json.dumps(current_page_selections or {}, ensure_ascii=False)

            cursor.execute('''
                UPDATE process_progress
                SET current_process_index = ?,
                    process_results = ?,
                    current_page_selections = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE work_order_id = ? AND status = 'in_progress'
            ''', (current_process_index, process_results_json, current_page_selections_json, work_order_id))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            print(f"更新流程进度失败: {e}")
            return False
        finally:
            conn.close()

    def complete_process_progress(self, work_order_id):
        """完成流程进度"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE process_progress
                SET status = 'completed',
                    updated_at = CURRENT_TIMESTAMP
                WHERE work_order_id = ? AND status = 'in_progress'
            ''', (work_order_id,))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            print(f"完成流程进度失败: {e}")
            return False
        finally:
            conn.close()

    def delete_process_progress(self, work_order_id):
        """删除流程进度"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                DELETE FROM process_progress WHERE work_order_id = ?
            ''', (work_order_id,))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            conn.rollback()
            print(f"删除流程进度失败: {e}")
            return False
        finally:
            conn.close()

    def get_all_in_progress_orders(self):
        """获取所有进行中的工单进度"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT pp.*, wo.column_a, wo.column_b, wo.column_c
                FROM process_progress pp
                JOIN work_orders wo ON pp.work_order_id = wo.id
                WHERE pp.status = 'in_progress'
                ORDER BY pp.updated_at DESC
            ''')

            records = cursor.fetchall()
            result = []

            for record in records:
                result.append({
                    'id': record['id'],
                    'work_order_id': record['work_order_id'],
                    'selected_processes': json.loads(record['selected_processes']),
                    'selected_process_details': json.loads(record['selected_process_details']),
                    'current_process_index': record['current_process_index'],
                    'process_results': json.loads(record['process_results']),
                    'current_page_selections': json.loads(record['current_page_selections'] or '{}'),
                    'status': record['status'],
                    'work_order_info': {
                        'column_a': record['column_a'],
                        'column_b': record['column_b'],
                        'column_c': record['column_c']
                    },
                    'created_at': record['created_at'],
                    'updated_at': record['updated_at']
                })

            return result

        except Exception as e:
            print(f"获取进行中工单失败: {e}")
            return []
        finally:
            conn.close()

    def save_archived_work_order(self, work_order_id, work_order_number, row_number):
        """保存已归档工单记录"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT OR REPLACE INTO archived_work_orders
                (work_order_id, work_order_number, row_number, archive_date)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (work_order_id, work_order_number, row_number))

            conn.commit()
            print(f"已保存归档工单记录: ID={work_order_id}, 工单号={work_order_number}, 行号={row_number}")
            return True

        except Exception as e:
            conn.rollback()
            print(f"保存归档工单记录失败: {e}")
            return False
        finally:
            conn.close()

    def get_archived_work_orders(self):
        """获取所有已归档工单"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT awo.*, wo.column_a, wo.column_b, wo.column_c, wo.column_q
                FROM archived_work_orders awo
                JOIN work_orders wo ON awo.work_order_id = wo.id
                ORDER BY awo.archive_date DESC
            ''')

            records = cursor.fetchall()
            result = []

            for record in records:
                result.append({
                    'id': record['id'],
                    'work_order_id': record['work_order_id'],
                    'work_order_number': record['work_order_number'],
                    'row_number': record['row_number'],
                    'archive_date': record['archive_date'],
                    'work_order_info': {
                        'column_a': record['column_a'],
                        'column_b': record['column_b'],
                        'column_c': record['column_c'],
                        'column_q': record['column_q']
                    },
                    'created_at': record['created_at']
                })

            return result

        except Exception as e:
            print(f"获取已归档工单失败: {e}")
            return []
        finally:
            conn.close()

    def delete_archived_work_order(self, work_order_id):
        """删除已归档工单记录"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('DELETE FROM archived_work_orders WHERE work_order_id = ?', (work_order_id,))
            affected_rows = cursor.rowcount
            conn.commit()

            print(f"已删除归档工单记录: work_order_id={work_order_id}, 影响行数={affected_rows}")
            return affected_rows > 0

        except Exception as e:
            conn.rollback()
            print(f"删除归档工单记录失败: {e}")
            return False
        finally:
            conn.close()

# 测试代码
if __name__ == "__main__":
    dm = DataManager()
    stats = dm.get_statistics()
    print("数据统计:", stats)
