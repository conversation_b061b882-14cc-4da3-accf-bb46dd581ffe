#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实使用场景：模拟用户处理工单后切换到新工单的情况
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_real_scenario():
    """测试真实使用场景"""
    print("🧪 开始测试真实使用场景...")
    
    app = QApplication(sys.argv)
    main_app = ExcelReaderApp()
    
    # 模拟加载数据
    print("📁 模拟加载Excel数据...")
    
    # 创建模拟数据
    mock_data = [
        ["行号", "工单编号", "类型", "描述", "号码", "原因", "状态"],  # 表头1
        ["", "", "", "", "", "", ""],  # 表头2
        [3, "WO001", "投诉", "网络问题", "13800138001", "信号差", "在途"],  # 工单1
        [4, "WO002", "投诉", "通话问题", "13800138002", "掉话", "在途"],  # 工单2
        [5, "WO003", "投诉", "流量问题", "13800138003", "网速慢", "在途"],  # 工单3
    ]
    
    main_app.current_data = mock_data
    
    # 初始化在途数据预览
    main_app.initialize_transit_preview()
    
    print("✅ 数据加载完成")
    
    # 测试场景：模拟真实用户操作
    def test_scenario():
        try:
            print("\n🎯 测试场景：模拟真实用户操作流程")
            
            # 步骤1：选择第一个工单并开始处理
            print("\n1️⃣ 步骤1：选择第一个工单并开始处理")
            first_row_data = mock_data[2]  # 第一个工单数据

            # 模拟点击表格项
            from PyQt6.QtWidgets import QTableWidgetItem
            mock_item = QTableWidgetItem()
            mock_item.setData(0, first_row_data)  # 存储行数据

            # 手动设置工单数据
            excel_row_number = int(first_row_data[0])
            new_work_order_id = main_app.get_work_order_id_by_row(excel_row_number)
            main_app.current_work_order_id = new_work_order_id
            main_app.current_work_order_data = first_row_data
            print(f"   设置当前工单ID: {new_work_order_id}")
            
            # 模拟用户开始流程处理
            print("   模拟用户选择流程：规划")
            main_app.selected_processes = ['规划']
            main_app.selected_process_details = ['规划:大网']
            main_app.current_process_index = 0
            main_app.process_results = {}
            
            # 模拟保存流程进度到数据库
            work_order_id = main_app.current_work_order_id
            print(f"   当前工单ID: {work_order_id}")
            
            # 创建流程进度记录
            success = main_app.data_manager.save_process_progress(
                work_order_id, 
                main_app.selected_processes,
                main_app.selected_process_details,
                main_app.current_process_index,
                main_app.process_results
            )
            print(f"   保存流程进度: {'成功' if success else '失败'}")
            
            # 模拟用户在规划页面做了选择
            print("   模拟用户在规划页面选择了'大网=是'")
            main_app.current_page_selections = {'big_network': '是'}
            
            # 更新流程进度（包含页面选择）
            main_app.data_manager.update_process_progress(
                work_order_id,
                main_app.current_process_index,
                main_app.process_results,
                main_app.current_page_selections
            )
            
            # 同时更新work_orders表
            updates = {'planning_big_network': '是'}
            main_app.update_work_order_columns(work_order_id, updates)
            print("   已保存选择到数据库")
            
            # 步骤2：切换到第二个工单（未处理的）
            print("\n2️⃣ 步骤2：切换到第二个工单（未处理的）")
            second_row_data = mock_data[3]  # 第二个工单数据

            # 手动切换工单
            excel_row_number_2 = int(second_row_data[0])
            new_work_order_id_2 = main_app.get_work_order_id_by_row(excel_row_number_2)

            # 清理之前的状态
            main_app.clear_previous_work_order_state()
            main_app.current_page_selections = {}
            main_app.clear_all_process_page_selections()

            # 设置新工单
            main_app.current_work_order_id = new_work_order_id_2
            main_app.current_work_order_data = second_row_data
            print(f"   切换到工单ID: {new_work_order_id_2}")
            
            # 检查第二个工单的状态
            print("   检查第二个工单的状态...")

            # 测试加载页面选择状态
            planning_selections = main_app.load_page_selections_from_database('planning')
            print(f"   第二个工单的规划选择状态: {planning_selections}")

            # 检查是否有pending_page_selections
            if hasattr(main_app, 'pending_page_selections'):
                print(f"   第二个工单的pending_page_selections: {main_app.pending_page_selections}")
            else:
                print("   第二个工单没有pending_page_selections")

            # 检查current_page_selections
            if hasattr(main_app, 'current_page_selections'):
                print(f"   第二个工单的current_page_selections: {main_app.current_page_selections}")
            else:
                print("   第二个工单没有current_page_selections")

            if planning_selections:
                print("   ❌ 错误：第二个工单显示了第一个工单的选择状态！")
            else:
                print("   ✅ 正确：第二个工单没有显示之前的选择状态")
            
            # 步骤3：再次切换回第一个工单
            print("\n3️⃣ 步骤3：再次切换回第一个工单")

            # 手动切换回第一个工单
            main_app.clear_previous_work_order_state()
            main_app.current_page_selections = {}
            main_app.clear_all_process_page_selections()

            main_app.current_work_order_id = new_work_order_id
            main_app.current_work_order_data = first_row_data
            print(f"   切换回工单ID: {new_work_order_id}")
            
            # 检查第一个工单的状态是否正确恢复
            print("   检查第一个工单的状态是否正确恢复...")
            
            # 测试加载页面选择状态
            planning_selections_restored = main_app.load_page_selections_from_database('planning')
            print(f"   第一个工单恢复的规划选择状态: {planning_selections_restored}")
            
            if planning_selections_restored.get('big_network') == '是':
                print("   ✅ 正确：第一个工单的选择状态正确恢复")
            else:
                print("   ❌ 错误：第一个工单的选择状态没有正确恢复")
            
            # 步骤4：再次切换到第三个工单（全新的）
            print("\n4️⃣ 步骤4：切换到第三个工单（全新的）")
            third_row_data = mock_data[4]  # 第三个工单数据

            # 手动切换到第三个工单
            excel_row_number_3 = int(third_row_data[0])
            new_work_order_id_3 = main_app.get_work_order_id_by_row(excel_row_number_3)

            main_app.clear_previous_work_order_state()
            main_app.current_page_selections = {}
            main_app.clear_all_process_page_selections()

            main_app.current_work_order_id = new_work_order_id_3
            main_app.current_work_order_data = third_row_data
            print(f"   切换到工单ID: {new_work_order_id_3}")
            
            # 检查第三个工单的状态
            print("   检查第三个工单的状态...")

            planning_selections_third = main_app.load_page_selections_from_database('planning')
            print(f"   第三个工单的规划选择状态: {planning_selections_third}")

            # 检查是否有pending_page_selections
            if hasattr(main_app, 'pending_page_selections'):
                print(f"   第三个工单的pending_page_selections: {main_app.pending_page_selections}")
            else:
                print("   第三个工单没有pending_page_selections")

            # 检查current_page_selections
            if hasattr(main_app, 'current_page_selections'):
                print(f"   第三个工单的current_page_selections: {main_app.current_page_selections}")
            else:
                print("   第三个工单没有current_page_selections")

            if planning_selections_third:
                print("   ❌ 错误：第三个工单显示了之前的选择状态！")
            else:
                print("   ✅ 正确：第三个工单没有显示之前的选择状态")
            
            print("\n🎉 测试完成！")
            
            # 总结测试结果
            if (not planning_selections and 
                not planning_selections_third and 
                planning_selections_restored.get('big_network') == '是'):
                print("✅ 修复验证成功：工单状态隔离正常工作")
                print("  - 未处理工单不显示之前的选择")
                print("  - 已处理工单正确恢复自己的选择")
                print("  - 新工单保持空白状态")
            else:
                print("❌ 修复验证失败：仍然存在状态污染问题")
            
            # 退出应用
            QTimer.singleShot(1000, app.quit)
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
            app.quit()
    
    # 延迟执行测试，确保界面完全初始化
    QTimer.singleShot(500, test_scenario)
    
    # 显示主窗口
    main_app.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_real_scenario()
