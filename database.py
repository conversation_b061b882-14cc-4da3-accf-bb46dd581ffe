# -*- coding: utf-8 -*-
import sqlite3
import os
from datetime import datetime
import json

class ExcelDataDB:
    def __init__(self, db_path="data/excel_data.db"):
        self.db_path = db_path
        # 确保data目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 1. 上传记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS upload_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_name VARCHAR(255) NOT NULL,
                    file_path VARCHAR(500),
                    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    total_rows INTEGER DEFAULT 0,
                    valid_rows INTEGER DEFAULT 0,
                    file_size INTEGER DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'active',
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 2. 工单数据表 (A-AD列完整结构)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS work_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    upload_id INTEGER NOT NULL,
                    row_number INTEGER NOT NULL,
                    
                    -- A-Q列 (单独标题列)
                    column_a TEXT,    -- A列
                    column_b TEXT,    -- B列  
                    column_c TEXT,    -- C列
                    column_d TEXT,    -- D列
                    column_e TEXT,    -- E列
                    column_f TEXT,    -- F列
                    column_g TEXT,    -- G列
                    column_h TEXT,    -- H列
                    column_i TEXT,    -- I列
                    column_j DATETIME,-- J列 (日期)
                    column_k DATETIME,-- K列 (日期)
                    column_l TEXT,    -- L列
                    column_m DECIMAL(10,6), -- M列 (经度)
                    column_n DECIMAL(10,6), -- N列 (纬度)
                    column_o TEXT,    -- O列
                    column_p TEXT,    -- P列 (原因)
                    column_q TEXT,    -- Q列
                    
                    -- R-S列 (规划大类)
                    planning_big_network TEXT,     -- R列 (规划,大网)
                    planning_non_big_network TEXT, -- S列 (规划,非大网)
                    
                    -- T-X列 (建设大类)
                    construction_property TEXT,    -- T列 (建设,物业)
                    construction_transmission TEXT,-- U列 (建设,传输)
                    construction_power TEXT,       -- V列 (建设,动力)
                    construction_tower TEXT,       -- W列 (建设,杆塔)
                    construction_equipment TEXT,   -- X列 (建设,设备安装)
                    
                    -- Y-Z列 (维护大类)
                    maintenance_sporadic TEXT,     -- Y列 (维护,零星)
                    maintenance_outsourced TEXT,   -- Z列 (维护,代维)
                    
                    -- AA-AB列 (优化大类)
                    optimization_antenna TEXT,     -- AA列 (优化,天线调整)
                    optimization_backend TEXT,     -- AB列 (优化,后台)
                    
                    -- AC-AD列 (客户大类)
                    customer_field_test TEXT,      -- AC列 (客户,现场测试)
                    customer_communication TEXT,   -- AD列 (客户,客户沟通)
                    
                    -- 计算字段
                    archive_duration INTEGER,      -- 归档时长(天数)
                    region_code VARCHAR(50),       -- 区域编码
                    
                    -- 元数据
                    is_valid BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (upload_id) REFERENCES upload_records(id)
                )
            ''')
            
            # 3. 数据分析缓存表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cache_key VARCHAR(100) NOT NULL UNIQUE,
                    cache_data TEXT NOT NULL,
                    cache_type VARCHAR(50) NOT NULL,
                    data_version INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME
                )
            ''')
            
            # 4. 流程进度表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS process_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    work_order_id INTEGER NOT NULL,
                    selected_processes TEXT NOT NULL,  -- JSON格式存储选择的流程列表
                    selected_process_details TEXT NOT NULL,  -- JSON格式存储流程详情
                    current_process_index INTEGER DEFAULT 0,  -- 当前处理到第几个流程
                    process_results TEXT,  -- JSON格式存储已完成流程的结果
                    current_page_selections TEXT,  -- JSON格式存储当前页面的选择状态
                    status VARCHAR(20) DEFAULT 'in_progress',  -- 状态: in_progress, completed, paused
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

                    FOREIGN KEY (work_order_id) REFERENCES work_orders(id)
                )
            ''')

            # 5. 已归档工单记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS archived_work_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    work_order_id INTEGER NOT NULL,
                    work_order_number TEXT NOT NULL,  -- B列工单号
                    row_number INTEGER NOT NULL,      -- Excel行号
                    archive_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

                    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
                    UNIQUE(work_order_id)  -- 确保每个工单只能归档一次
                )
            ''')

            # 6. 系统配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key VARCHAR(100) NOT NULL UNIQUE,
                    config_value TEXT,
                    config_type VARCHAR(20) DEFAULT 'string',
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            self.create_indexes(cursor)
            
            # 插入初始配置
            self.insert_initial_config(cursor)

            conn.commit()
            print("数据库初始化完成")

        except Exception as e:
            conn.rollback()
            print(f"数据库初始化失败: {e}")
            raise
        finally:
            conn.close()
    
    def create_indexes(self, cursor):
        """创建索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_work_orders_upload_id ON work_orders(upload_id)",
            "CREATE INDEX IF NOT EXISTS idx_work_orders_column_j ON work_orders(column_j)",
            "CREATE INDEX IF NOT EXISTS idx_work_orders_column_k ON work_orders(column_k)",
            "CREATE INDEX IF NOT EXISTS idx_work_orders_coordinates ON work_orders(column_m, column_n)",
            "CREATE INDEX IF NOT EXISTS idx_work_orders_region ON work_orders(region_code)",
            "CREATE INDEX IF NOT EXISTS idx_upload_records_status ON upload_records(status)",
            "CREATE INDEX IF NOT EXISTS idx_analysis_cache_key ON analysis_cache(cache_key)",
            "CREATE INDEX IF NOT EXISTS idx_work_orders_valid ON work_orders(is_valid)",
            "CREATE INDEX IF NOT EXISTS idx_process_progress_work_order ON process_progress(work_order_id)",
            "CREATE INDEX IF NOT EXISTS idx_process_progress_status ON process_progress(status)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def insert_initial_config(self, cursor):
        """插入初始配置"""
        configs = [
            ('data_version', '1', 'int', '数据版本号'),
            ('last_backup_time', '', 'string', '最后备份时间'),
            ('cache_enabled', 'true', 'string', '是否启用缓存'),
            ('max_cache_size', '100', 'int', '最大缓存条数')
        ]
        
        for config_key, config_value, config_type, description in configs:
            cursor.execute('''
                INSERT OR IGNORE INTO system_config 
                (config_key, config_value, config_type, description) 
                VALUES (?, ?, ?, ?)
            ''', (config_key, config_value, config_type, description))

# 初始化数据库
if __name__ == "__main__":
    db = ExcelDataDB()
    print("数据库创建完成！")
