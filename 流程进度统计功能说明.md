# 流程进度统计功能修改说明

## 修改内容

已成功将在途数据处理页面的数据统计区域修改为显示各个流程进度的工单统计，替换了原来的简单在途/已归档统计。

## 主要修改

### 1. 统计内容变更
**原来显示**：
- 总数据: X 条 | 在途数据: Y 条 | 已归档: Z 条

**现在显示**：
- 未开始: X | 规划: Y | 建设: Z | 维护: A | 优化: B | 客户: C | 已完成: D

### 2. 修改的函数

#### `update_process_stats()`
- 修改为调用 `get_process_progress_stats()` 获取流程进度统计
- 格式化显示各个流程阶段的工单数量

#### `get_process_progress_stats()` (新增)
- 统计各个流程进度的工单数量
- 只统计在途数据（Q列为空或"在途"状态）
- 通过 `get_process_progress_text()` 获取每行的流程进度
- 根据流程进度文本分类统计

#### `update_transit_stats()`
- 保持原有的在途数据页面统计标签不变
- 修改数据概览卡片中的统计标签为流程进度统计
- 统一错误处理和无数据状态显示

### 3. 流程分类逻辑

根据 `get_process_progress_text()` 返回的文本进行分类：

- **未开始**：返回 "未开始"
- **已完成**：返回 "已完成"
- **规划**：包含 "规划进行中"
- **建设**：包含 "建设进行中"
- **维护**：包含 "维护进行中"
- **优化**：包含 "优化进行中"
- **客户**：包含 "客户进行中"

### 4. 数据来源

流程进度信息来自：
1. 数据库中的 `process_progress` 表
2. 通过工单ID关联Excel行号
3. 根据当前流程索引和选择的流程列表确定当前状态

## 功能特点

### 1. 实时统计
- 每次刷新数据时自动更新流程进度统计
- 只统计在途数据，已归档数据不参与流程统计

### 2. 准确分类
- 基于数据库中保存的实际流程进度状态
- 支持多种流程组合和当前进度追踪

### 3. 界面一致性
- 保持原有的界面布局和样式
- 统计信息显示格式清晰易读

### 4. 错误处理
- 完善的异常处理机制
- 无数据时显示默认的零值统计

## 使用场景

### 1. 工单管理
- 快速了解各个流程阶段的工单分布
- 识别流程瓶颈和处理重点

### 2. 进度监控
- 实时监控各流程的工单数量
- 帮助合理分配处理资源

### 3. 数据分析
- 为流程优化提供数据支持
- 分析各阶段的处理效率

## 技术实现

### 1. 数据获取
```python
def get_process_progress_stats(self):
    # 遍历在途数据
    # 获取每行的流程进度文本
    # 根据文本内容分类统计
```

### 2. 状态判断
```python
# 基于流程进度文本进行分类
if "规划进行中" in progress_text:
    stats['规划'] += 1
elif "建设进行中" in progress_text:
    stats['建设'] += 1
# ... 其他流程分类
```

### 3. 界面更新
```python
# 格式化统计信息并更新显示
stats_text = f"未开始: {stats['未开始']} | 规划: {stats['规划']} | ..."
self.process_stats_label.setText(stats_text)
```

## 注意事项

1. **数据依赖**：需要数据库中有流程进度记录才能正确统计
2. **状态同步**：流程进度统计与实际流程处理状态保持同步
3. **性能考虑**：大量数据时统计可能需要一定时间
4. **扩展性**：如果增加新的流程类型，需要相应更新分类逻辑
