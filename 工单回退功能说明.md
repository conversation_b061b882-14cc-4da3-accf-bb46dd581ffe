# 工单回退功能说明

## 功能概述

新增了工单回退功能，允许将已经完成流程处理并标记为归档的工单恢复到在途状态，并清除所有流程处理结果。这样可以重新对工单进行流程处理。

## 功能入口

### 方式一：归档工单管理页面（推荐）
1. 点击侧边栏的 **"📋 归档工单管理"** 按钮
2. 在归档工单列表中右键点击要回退的工单
3. 选择 **"↩️ 回退工单"**

### 方式二：在途数据处理页面（已废弃）
~~在**在途数据处理页面**的数据表格中右键回退~~
*注：由于已归档工单不会在在途数据中显示，此方式已不可用*

## 归档工单管理页面

### 页面功能
- **查看归档历史**：显示所有已归档的工单信息
- **工单详情**：包含工单号、A列、C列、归档时间、Excel行号等信息
- **回退操作**：支持单个工单的回退操作
- **数据刷新**：实时更新归档工单列表

### 表格列说明
- **工单号**：B列的工单编号
- **A列**：工单的A列内容
- **C列**：工单的C列内容
- **归档时间**：工单被标记为归档的时间
- **Excel行号**：工单在Excel文件中的行号
- **状态**：当前工单状态（通常为"已归档"）

### 操作流程
1. **进入管理页面**：点击侧边栏的"📋 归档工单管理"按钮
2. **查看归档工单**：页面会自动加载所有已归档的工单列表
3. **选择工单**：在表格中找到需要回退的工单行
4. **右键菜单**：右键点击该行，弹出上下文菜单
5. **选择回退**：点击菜单中的"↩️ 回退工单"选项
6. **确认操作**：系统会弹出确认对话框
7. **执行回退**：点击"是"确认执行回退操作
8. **完成**：工单从归档列表中消失，重新出现在在途数据列表中

## 回退操作内容

### 1. 确认对话框
- 显示要回退的工单行号
- 详细说明回退操作的影响
- 提醒操作不可撤销
- 需要用户确认才能继续

### 2. 回退操作步骤

#### 步骤1：清除数据库流程结果
清除 `work_orders` 表中该工单的所有流程处理结果：
- **R-S列**（规划）：`planning_big_network`, `planning_non_big_network`
- **T-X列**（建设）：`construction_property`, `construction_transmission`, `construction_power`, `construction_tower`, `construction_equipment`
- **Y-Z列**（维护）：`maintenance_sporadic`, `maintenance_outsourced`
- **AA-AB列**（优化）：`optimization_antenna`, `optimization_backend`
- **AC-AD列**（客户）：`customer_field_test`, `customer_communication`

#### 步骤2：删除流程进度记录
从 `process_progress` 表中删除该工单的所有流程进度记录：
- 选择的流程列表
- 流程详情
- 当前进度索引
- 流程结果
- 页面选择状态

#### 步骤3：更新归档状态
- 将Q列（归档状态）更新为"在途"
- 同时清除内存中R-AD列的所有数据

#### 步骤4：刷新界面
- 刷新在途数据表格显示
- 更新流程进度统计
- 显示操作成功消息

## 技术实现

### 核心函数

#### 归档工单管理相关
- `create_archived_management_page()` - 创建归档工单管理页面
- `show_archived_management_page()` - 显示归档工单管理页面
- `refresh_archived_data()` - 刷新归档工单数据显示
- `show_archived_context_menu()` - 显示归档工单右键菜单
- `rollback_archived_work_order()` - 处理归档工单回退用户交互
- `perform_archived_work_order_rollback()` - 执行归档工单回退操作
- `update_work_order_archive_status()` - 更新工单归档状态

#### 原有回退功能（在途数据）
- `rollback_work_order()` - 在途数据回退主入口函数
- `perform_work_order_rollback(excel_row_number)` - 执行在途工单回退操作

#### `clear_work_order_process_results(work_order_id)`
- 清除数据库中的流程处理结果
- 将R-AD列对应的数据库字段设置为NULL
- 更新 `updated_at` 时间戳

#### `delete_process_progress(work_order_id)`
- 删除流程进度记录
- 从 `process_progress` 表中删除对应记录

#### `update_memory_data_status(excel_row_number, status)`
- 更新内存中的数据状态
- 设置Q列为指定状态
- 清除R-AD列的内存数据

### 数据库操作

#### 新增归档工单记录表
```sql
-- 创建归档工单记录表
CREATE TABLE archived_work_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    work_order_id INTEGER NOT NULL,
    work_order_number TEXT,
    row_number INTEGER,
    archive_date TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id)
);
```

#### 归档工单管理操作
```sql
-- 保存归档工单记录
INSERT INTO archived_work_orders (work_order_id, work_order_number, row_number, archive_date)
VALUES (?, ?, ?, ?);

-- 获取归档工单列表
SELECT a.*, w.column_a, w.column_c, w.column_q
FROM archived_work_orders a
LEFT JOIN work_orders w ON a.work_order_id = w.id
ORDER BY a.created_at DESC;

-- 删除归档工单记录
DELETE FROM archived_work_orders WHERE work_order_id = ?;
```

#### 工单回退操作
```sql
-- 清除流程处理结果
UPDATE work_orders SET
    planning_big_network = NULL,
    planning_non_big_network = NULL,
    construction_property = NULL,
    construction_transmission = NULL,
    construction_power = NULL,
    construction_tower = NULL,
    construction_equipment = NULL,
    maintenance_sporadic = NULL,
    maintenance_outsourced = NULL,
    optimization_antenna = NULL,
    optimization_backend = NULL,
    customer_field_test = NULL,
    customer_communication = NULL,
    column_q = ?,
    updated_at = CURRENT_TIMESTAMP
WHERE id = ?;

-- 删除流程进度记录
DELETE FROM process_progress WHERE work_order_id = ?;
```

## 使用场景

### 1. 流程处理错误
- 发现之前的流程选择有误
- 需要重新选择正确的流程组合

### 2. 处理结果错误
- 某些流程项目的"是/否"选择有误
- 需要重新进行流程处理

### 3. 流程变更
- 业务流程发生变化
- 需要按新流程重新处理工单

### 4. 质量检查
- 质量审核发现问题
- 需要重新处理以确保质量

## 安全机制

### 1. 确认对话框
- 详细说明操作影响
- 明确提示不可撤销
- 默认选择"否"

### 2. 数据完整性
- 使用事务确保数据一致性
- 操作失败时自动回滚
- 详细的错误日志记录

### 3. 权限控制
- 只能回退当前用户有权限的工单
- 基于行号和工单ID的双重验证

## 注意事项

### 1. 操作不可撤销
- 一旦确认回退，无法恢复之前的处理结果
- 建议在重要操作前备份数据

### 2. 数据同步
- 回退操作会同时更新数据库和内存数据
- 确保界面显示与数据库状态一致

### 3. 流程重新开始
- 回退后的工单需要重新选择流程
- 之前的流程进度和结果完全清除

### 4. 统计信息更新
- 回退后会自动更新流程进度统计
- 该工单会重新计入"未开始"状态

## 错误处理

### 1. 工单ID获取失败
- 提示无法找到对应的工单记录
- 可能原因：数据不同步或行号错误

### 2. 数据库操作失败
- 显示具体的错误信息
- 记录详细的错误日志

### 3. 内存数据更新失败
- 提示数据状态更新失败
- 建议重新加载数据

## 后续扩展

### 1. 批量回退
- 支持选择多个工单进行批量回退
- 提供批量操作的进度显示

### 2. 回退历史
- 记录回退操作的历史
- 提供回退操作的审计日志

### 3. 部分回退
- 支持只回退特定流程的处理结果
- 保留其他流程的处理状态
