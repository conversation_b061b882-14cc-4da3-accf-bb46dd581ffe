#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
归档工单管理功能测试脚本
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_archived_management():
    """测试归档工单管理功能"""
    app = QApplication(sys.argv)
    
    # 创建主应用
    main_app = ExcelReaderApp()
    main_app.show()
    
    def check_archived_page():
        """检查归档工单管理页面是否正常创建"""
        try:
            # 检查页面是否存在
            if hasattr(main_app, 'archived_management_page'):
                print("✅ 归档工单管理页面创建成功")
                
                # 检查按钮是否存在
                if hasattr(main_app, 'archived_mgmt_btn'):
                    print("✅ 归档工单管理按钮创建成功")
                    
                    # 模拟点击按钮
                    main_app.show_archived_management_page()
                    print("✅ 归档工单管理页面显示成功")
                    
                    # 检查表格是否存在
                    if hasattr(main_app, 'archived_table'):
                        print("✅ 归档工单表格创建成功")
                        print(f"表格列数: {main_app.archived_table.columnCount()}")
                        print(f"表格行数: {main_app.archived_table.rowCount()}")
                    else:
                        print("❌ 归档工单表格未创建")
                        
                else:
                    print("❌ 归档工单管理按钮未创建")
            else:
                print("❌ 归档工单管理页面未创建")
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
        
        # 退出应用
        QTimer.singleShot(1000, app.quit)
    
    # 延迟执行检查，确保界面完全加载
    QTimer.singleShot(500, check_archived_page)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    print("🧪 开始测试归档工单管理功能...")
    test_archived_management()
