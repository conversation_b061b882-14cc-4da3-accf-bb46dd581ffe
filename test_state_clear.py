#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态清理功能
直接测试clear_previous_work_order_state方法
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_state_clear():
    """测试状态清理功能"""
    app = QApplication(sys.argv)
    
    # 创建主应用
    main_app = ExcelReaderApp()
    main_app.show()
    
    def test_clear_logic():
        """测试清理逻辑"""
        try:
            print("🧪 开始测试状态清理逻辑...")
            
            # 1. 设置一些模拟状态
            print("📝 设置模拟状态...")
            main_app.current_page_selections = {
                'planning': {
                    'big_network': '是',
                    'non_big_network': '否'
                },
                'construction': {
                    'property': '是',
                    'transmission': '否',
                    'power': '是'
                },
                'maintenance': {
                    'sporadic': '否',
                    'outsourced': '是'
                }
            }
            
            main_app.process_results = {
                'planning': {'status': 'completed'},
                'construction': {'status': 'in_progress'}
            }
            
            main_app.selected_processes = ['规划', '建设', '维护']
            main_app.current_process_index = 1
            
            print(f"设置前状态:")
            print(f"  current_page_selections: {main_app.current_page_selections}")
            print(f"  process_results: {main_app.process_results}")
            print(f"  selected_processes: {main_app.selected_processes}")
            print(f"  current_process_index: {main_app.current_process_index}")
            
            # 2. 调用清理方法
            print("\n🧹 调用状态清理方法...")
            main_app.clear_previous_work_order_state()
            
            # 3. 检查清理结果
            print(f"\n清理后状态:")
            print(f"  current_page_selections: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
            print(f"  process_results: {getattr(main_app, 'process_results', 'NOT_SET')}")
            print(f"  selected_processes: {getattr(main_app, 'selected_processes', 'NOT_SET')}")
            print(f"  current_process_index: {getattr(main_app, 'current_process_index', 'NOT_SET')}")
            
            # 4. 验证结果
            success = True
            
            if getattr(main_app, 'current_page_selections', None) != {}:
                print("❌ current_page_selections 未被正确清理")
                success = False
            
            if getattr(main_app, 'process_results', None) != {}:
                print("❌ process_results 未被正确清理")
                success = False
            
            if getattr(main_app, 'selected_processes', None) != []:
                print("❌ selected_processes 未被正确清理")
                success = False
            
            if getattr(main_app, 'current_process_index', None) != 0:
                print("❌ current_process_index 未被正确清理")
                success = False
            
            if success:
                print("✅ 状态清理测试通过：所有状态都被正确清理")
            else:
                print("❌ 状态清理测试失败：部分状态未被清理")
            
            # 5. 测试工单切换时的状态清理
            print("\n🔄 测试工单切换时的状态清理...")
            
            # 重新设置状态
            main_app.current_page_selections = {
                'planning': {'big_network': '是'}
            }
            main_app.current_work_order_id = 12345
            
            print(f"切换前: current_page_selections = {main_app.current_page_selections}")
            print(f"切换前: current_work_order_id = {main_app.current_work_order_id}")
            
            # 模拟工单切换（使用空的行数据）
            row_data = [''] * 20  # 模拟空行数据
            if hasattr(main_app, 'handle_work_order_selection'):
                main_app.handle_work_order_selection(row_data)
            
            print(f"切换后: current_page_selections = {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
            print(f"切换后: current_work_order_id = {getattr(main_app, 'current_work_order_id', 'NOT_SET')}")
            
            # 验证工单切换后的状态
            if getattr(main_app, 'current_page_selections', None) == {}:
                print("✅ 工单切换时状态清理正常")
            else:
                print("❌ 工单切换时状态清理失败")
                print(f"残留状态: {getattr(main_app, 'current_page_selections', {})}")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 退出应用
        QTimer.singleShot(1000, app.quit)
    
    # 延迟执行测试，确保界面完全加载
    QTimer.singleShot(2000, test_clear_logic)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    print("🧪 开始测试状态清理功能...")
    test_state_clear()
