#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库状态
检查数据库中是否有脏数据导致状态污染
"""

import sys
import os
import json
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def debug_database_state():
    """调试数据库状态"""
    app = QApplication(sys.argv)
    
    # 创建主应用
    main_app = ExcelReaderApp()
    main_app.show()
    
    def check_database():
        """检查数据库状态"""
        try:
            print("🔍 开始检查数据库状态...")
            
            if not hasattr(main_app, 'data_manager'):
                print("❌ 数据管理器不存在")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 检查process_progress表
            print("\n📊 检查process_progress表...")
            conn = main_app.data_manager.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT work_order_id, current_page_selections, status, created_at
                FROM process_progress
                ORDER BY created_at DESC
                LIMIT 10
            ''')
            
            progress_records = cursor.fetchall()
            print(f"找到 {len(progress_records)} 条流程进度记录")
            
            for i, record in enumerate(progress_records):
                work_order_id = record['work_order_id']
                current_page_selections = record['current_page_selections']
                status = record['status']
                created_at = record['created_at']
                
                print(f"\n记录 {i+1}:")
                print(f"  工单ID: {work_order_id}")
                print(f"  状态: {status}")
                print(f"  创建时间: {created_at}")
                
                if current_page_selections:
                    try:
                        selections = json.loads(current_page_selections)
                        print(f"  页面选择状态: {selections}")
                        
                        # 检查是否有非空的选择状态
                        has_selections = False
                        for process_key, process_selections in selections.items():
                            if process_selections:
                                has_selections = True
                                print(f"    {process_key}: {process_selections}")
                        
                        if has_selections:
                            print(f"  ⚠️ 工单 {work_order_id} 有保存的选择状态")
                        else:
                            print(f"  ✅ 工单 {work_order_id} 选择状态为空")
                            
                    except json.JSONDecodeError:
                        print(f"  ❌ 无法解析选择状态JSON: {current_page_selections}")
                else:
                    print(f"  ✅ 工单 {work_order_id} 无选择状态")
            
            # 检查work_orders表中的R-AD列
            print("\n📊 检查work_orders表中的流程结果列...")
            cursor.execute('''
                SELECT id, row_number,
                       planning_big_network, planning_non_big_network,
                       construction_property, construction_transmission, construction_power,
                       maintenance_sporadic, maintenance_outsourced,
                       optimization_antenna, optimization_backend,
                       customer_field_test, customer_communication,
                       column_q
                FROM work_orders
                WHERE column_q = '在途'
                ORDER BY id DESC
                LIMIT 5
            ''')
            
            work_order_records = cursor.fetchall()
            print(f"找到 {len(work_order_records)} 条在途工单记录")
            
            for i, record in enumerate(work_order_records):
                work_order_id = record['id']
                row_number = record['row_number']
                column_q = record['column_q']
                
                print(f"\n在途工单 {i+1}:")
                print(f"  工单ID: {work_order_id}")
                print(f"  行号: {row_number}")
                print(f"  状态: {column_q}")
                
                # 检查流程结果列
                process_columns = [
                    ('规划大网', record['planning_big_network']),
                    ('规划非大网', record['planning_non_big_network']),
                    ('建设产权', record['construction_property']),
                    ('建设传输', record['construction_transmission']),
                    ('建设电源', record['construction_power']),
                    ('维护零星', record['maintenance_sporadic']),
                    ('维护外包', record['maintenance_outsourced']),
                    ('优化天线', record['optimization_antenna']),
                    ('优化后台', record['optimization_backend']),
                    ('客户现场', record['customer_field_test']),
                    ('客户沟通', record['customer_communication'])
                ]
                
                has_process_results = False
                for col_name, col_value in process_columns:
                    if col_value and col_value.strip():
                        has_process_results = True
                        print(f"    {col_name}: {col_value}")
                
                if has_process_results:
                    print(f"  ⚠️ 工单 {work_order_id} 有流程处理结果")
                else:
                    print(f"  ✅ 工单 {work_order_id} 无流程处理结果")
            
            conn.close()
            
            # 检查当前工单状态
            if hasattr(main_app, 'current_work_order_id') and main_app.current_work_order_id:
                print(f"\n🎯 当前工单ID: {main_app.current_work_order_id}")
                print(f"当前页面选择状态: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
            
        except Exception as e:
            print(f"❌ 检查数据库状态时出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 退出应用
        QTimer.singleShot(2000, app.quit)
    
    # 延迟执行检查，确保界面完全加载
    QTimer.singleShot(3000, check_database)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    print("🔍 开始调试数据库状态...")
    debug_database_state()
