#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单状态缓存bug修复
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_work_order_state_isolation():
    """测试工单状态隔离"""
    print("🧪 开始测试工单状态隔离...")

    app = QApplication(sys.argv)
    main_app = ExcelReaderApp()

    # 模拟加载数据
    print("📁 模拟加载Excel数据...")

    # 创建模拟数据
    mock_data = [
        ["行号", "工单编号", "类型", "描述", "号码", "原因", "状态"],  # 表头1
        ["", "", "", "", "", "", ""],  # 表头2
        [3, "WO001", "投诉", "网络问题", "13800138001", "信号差", "在途"],  # 工单1
        [4, "WO002", "投诉", "通话问题", "13800138002", "掉话", "在途"],  # 工单2
        [5, "WO003", "投诉", "流量问题", "13800138003", "网速慢", "在途"],  # 工单3
    ]

    main_app.current_data = mock_data

    # 初始化在途数据预览
    main_app.initialize_transit_preview()

    print("✅ 数据加载完成")

    # 测试场景：模拟用户操作
    def test_scenario():
        try:
            print("\n🎯 测试场景：验证未处理工单不会显示之前的选择状态")

            # 1. 选择第一个工单并进入详细页面
            print("1️⃣ 选择第一个工单 (行号3)")
            first_row_data = mock_data[2]  # 第一个工单数据
            main_app.show_transit_detail_page_with_data(first_row_data)

            # 检查状态是否被清理
            if hasattr(main_app, 'current_page_selections'):
                print(f"   当前页面选择状态: {main_app.current_page_selections}")
            else:
                print("   ✅ 当前页面选择状态已清理")

            # 测试加载页面选择状态的逻辑
            print("\n🔍 测试加载页面选择状态的逻辑...")

            # 测试规划流程页面
            planning_selections = main_app.load_page_selections_from_database('planning')
            print(f"   规划流程选择状态: {planning_selections}")

            # 测试建设流程页面
            construction_selections = main_app.load_page_selections_from_database('construction')
            print(f"   建设流程选择状态: {construction_selections}")

            # 测试维护流程页面
            maintenance_selections = main_app.load_page_selections_from_database('maintenance')
            print(f"   维护流程选择状态: {maintenance_selections}")

            # 2. 切换到第二个工单
            print("\n2️⃣ 切换到第二个工单 (行号4)")
            second_row_data = mock_data[3]  # 第二个工单数据
            main_app.show_transit_detail_page_with_data(second_row_data)

            # 再次测试加载页面选择状态的逻辑
            print("🔍 再次测试加载页面选择状态的逻辑...")

            planning_selections2 = main_app.load_page_selections_from_database('planning')
            print(f"   规划流程选择状态: {planning_selections2}")

            construction_selections2 = main_app.load_page_selections_from_database('construction')
            print(f"   建设流程选择状态: {construction_selections2}")

            # 3. 再次切换到第三个工单
            print("\n3️⃣ 切换到第三个工单 (行号5)")
            third_row_data = mock_data[4]  # 第三个工单数据
            main_app.show_transit_detail_page_with_data(third_row_data)

            # 最后一次测试
            print("🔍 最后一次测试加载页面选择状态的逻辑...")

            planning_selections3 = main_app.load_page_selections_from_database('planning')
            print(f"   规划流程选择状态: {planning_selections3}")

            print("\n🎉 测试完成！")
            if not planning_selections and not construction_selections and not maintenance_selections:
                print("✅ 修复验证成功：未处理工单不会显示之前的选择状态")
            else:
                print("❌ 修复验证失败：仍然显示了之前的选择状态")

            # 退出应用
            QTimer.singleShot(1000, app.quit)

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
            app.quit()

    # 延迟执行测试，确保界面完全初始化
    QTimer.singleShot(500, test_scenario)

    # 显示主窗口
    main_app.show()

    # 运行应用
    sys.exit(app.exec())

def show_transit_detail_page_with_data(self, row_data):
    """修改后的显示在途数据详细页面方法，用于测试"""
    try:
        print(f"🔍 显示工单详细页面: {row_data}")
        
        # 获取Excel行号
        excel_row_number = int(row_data[0])
        
        # 获取工单ID
        new_work_order_id = self.get_work_order_id_by_row(excel_row_number)
        print(f"🔍 准备切换到工单ID: {new_work_order_id} (Excel行号: {excel_row_number})")
        
        # 强化的工单ID比较逻辑
        current_id = getattr(self, 'current_work_order_id', None)
        
        # 检查是否是同一个工单
        if (current_id is not None and
            new_work_order_id is not None and
            str(current_id) == str(new_work_order_id)):
            print(f"✅ 仍然是同一个工单 (ID: {current_id})，保持当前状态")
        else:
            print(f"🔄 切换到新工单 (从 {current_id} 到 {new_work_order_id})，强制清理之前的状态")
            
            # 强制清理之前工单的状态
            try:
                self.clear_previous_work_order_state()
                self.current_page_selections = {}
                self.clear_all_process_page_selections()
                print("🧹 已清理页面选择状态缓存和UI状态")
            except Exception as clear_error:
                print(f"⚠️ 清理状态时出错: {clear_error}")
            
            # 设置新的工单ID
            self.current_work_order_id = new_work_order_id
            self.current_work_order_data = row_data
        
        # 切换到详细页面
        self.stacked_widget.setCurrentWidget(self.transit_detail_page)
        self.update_transit_detail_info(row_data)
        
    except Exception as e:
        print(f"❌ 显示工单详细页面时出错: {e}")

# 将测试方法添加到ExcelReaderApp类
ExcelReaderApp.show_transit_detail_page_with_data = show_transit_detail_page_with_data

if __name__ == "__main__":
    test_work_order_state_isolation()
