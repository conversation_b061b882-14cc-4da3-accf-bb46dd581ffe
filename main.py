import sys
import os
import signal
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QFileDialog,
                             QTableWidget, QTableWidgetItem, QTextEdit,
                             QSplitter, QFrame, QScrollArea, QMessageBox,
                             QProgressBar, QGroupBox, QGridLayout, QComboBox,
                             QStackedWidget, QMenu, QLineEdit, QCheckBox,
                             QRadioButton, QButtonGroup, QDialog, QInputDialog,
                             QSizePolicy, QHeaderView, QAbstractItemView)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon, QPixmap, QPainter, QLinearGradient, QKeySequence, QShortcut
from openpyxl import load_workbook
import pandas as pd
import matplotlib
matplotlib.use('Qt5Agg')  # 设置matplotlib后端
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates

# 设置matplotlib兼容性
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")
from datetime import datetime
import numpy as np
from collections import defaultdict
import json
import os
from matplotlib.patches import Polygon
from matplotlib.collections import PatchCollection
from data_manager import DataManager

class ModernButton(QPushButton):
    def __init__(self, text, primary=False, height=40):  # 减小默认高度
        super().__init__(text)
        self.primary = primary
        self.setFixedHeight(height)
        self.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Medium))  # 减小字体
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.update_style()

    def update_style(self):
        if self.primary:
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #667eea, stop:1 #764ba2);
                    color: white;
                    border: none;
                    border-radius: 25px;
                    font-weight: bold;
                    padding: 0 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5a6fd8, stop:1 #6a4190);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4e5bc6, stop:1 #5e377e);
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.1);
                    color: #333;
                    border: 2px solid rgba(102, 126, 234, 0.3);
                    border-radius: 25px;
                    font-weight: bold;
                    padding: 0 20px;
                }
                QPushButton:hover {
                    background: rgba(102, 126, 234, 0.1);
                    border: 2px solid rgba(102, 126, 234, 0.6);
                    color: #667eea;
                }
                QPushButton:pressed {
                    background: rgba(102, 126, 234, 0.2);
                }
            """)

class SidebarButton(QPushButton):
    def __init__(self, text, icon_text="📊"):
        super().__init__()
        self.setText(f"{icon_text}  {text}")
        self.setFixedHeight(60)
        self.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Medium))
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setCheckable(True)
        self.update_style()

    def update_style(self):
        self.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #666;
                border: none;
                border-radius: 15px;
                text-align: left;
                padding: 15px 20px;
                margin: 5px;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.2), stop:1 rgba(118, 75, 162, 0.2));
                color: #667eea;
                border-left: 4px solid #667eea;
            }
        """)

class ExcelReaderThread(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal(object)
    error = pyqtSignal(str)

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path

    def run(self):
        try:
            self.progress.emit(20)
            workbook = load_workbook(self.file_path)
            self.progress.emit(40)

            sheet = workbook.active
            data = []
            max_row = sheet.max_row # type: ignore

            for i, row in enumerate(sheet.iter_rows(values_only=True)): # type: ignore
                data.append(row)
                if i % 100 == 0:
                    progress_val = 40 + int((i / max_row) * 50)
                    self.progress.emit(min(progress_val, 90))

            self.progress.emit(100)
            self.finished.emit(data)

        except Exception as e:
            self.error.emit(str(e))

class ExcelReaderApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_data = None
        self.last_file_path = None
        self.yunnan_map_cache = None  # 缓存云南省地图数据
        self.data_manager = DataManager()  # 数据管理器

        # 初始化按钮列表
        self.analysis_buttons = []
        self.archive_analysis_buttons = []

        self.init_ui()
        self.setup_shortcuts()
        self.load_existing_data()  # 加载已存在的数据

        # 确保在途数据预览在启动时就被初始化
        if self.current_data:
            QTimer.singleShot(100, self.initialize_transit_preview)

    def initialize_transit_preview(self):
        """初始化在途数据预览"""
        try:
            if hasattr(self, 'transit_table') and hasattr(self, 'transit_stats_label'):
                self.update_transit_stats()
                self.refresh_transit_preview_data()
        except Exception as e:
            print(f"初始化在途数据预览失败: {e}")

    def init_ui(self):
        self.setWindowTitle("基于python的可视化投诉工具")

        # 获取屏幕尺寸并设置合适的窗口大小
        screen = QApplication.primaryScreen().geometry()
        screen_width = screen.width()
        screen_height = screen.height()

        # 设置窗口大小为屏幕的80%，但不超过1400x900，不小于1000x700
        window_width = min(int(screen_width * 0.8), 1400)
        window_width = max(window_width, 1000)
        window_height = min(int(screen_height * 0.8), 900)
        window_height = max(window_height, 700)

        # 居中显示
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.setGeometry(x, y, window_width, window_height)
        self.setMinimumSize(1000, 700)  # 减小最小尺寸

        # 设置主题样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QFrame {
                background: white;
                border-radius: 15px;
            }
            QLabel {
                color: #333;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 创建侧边栏
        self.create_sidebar(main_layout)

        # 创建主内容区域
        self.create_main_content(main_layout)

        # 设置默认选中第一个选项
        self.import_btn.setChecked(True)
        self.show_import_page()

    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+C 强制退出程序
        self.exit_shortcut = QShortcut(QKeySequence("Ctrl+C"), self)
        self.exit_shortcut.activated.connect(self.force_exit)

        # 也可以添加其他快捷键
        self.quit_shortcut = QShortcut(QKeySequence("Ctrl+Q"), self)
        self.quit_shortcut.activated.connect(self.force_exit)

        # Esc键退出
        self.esc_shortcut = QShortcut(QKeySequence("Escape"), self)
        self.esc_shortcut.activated.connect(self.close)

    def force_exit(self):
        """强制退出程序"""
        print("\n检测到Ctrl+C，正在强制退出程序...")

        # 如果有正在运行的线程，先停止它们
        if hasattr(self, 'worker_thread') and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait(1000)  # 等待最多1秒

        # 强制退出应用程序
        QApplication.quit()
        sys.exit(0)

    def create_sidebar(self, main_layout):
        # 侧边栏框架
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(280)
        sidebar_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(20, 30, 20, 30)
        sidebar_layout.setSpacing(10)

        # 标题
        title_label = QLabel("Excel工具")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #333; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(title_label)

        # 功能按钮
        self.import_btn = SidebarButton("导入Excel文件", "📁")
        self.archive_viz_btn = SidebarButton("归档可视化", "📊")
        self.transit_data_btn = SidebarButton("在途数据处理", "🚛")
        self.archived_mgmt_btn = SidebarButton("归档工单管理", "📋")
        self.number_query_btn = SidebarButton("号码次数统计", "🔍")
        self.test_time_edit_btn = SidebarButton("修改现场测试时间", "⏰")
        self.data_mgmt_btn = SidebarButton("数据管理", "🗄️")

        self.import_btn.clicked.connect(self.show_import_page)
        self.archive_viz_btn.clicked.connect(self.show_archive_visualization_page)
        self.transit_data_btn.clicked.connect(self.show_transit_data_page)
        self.archived_mgmt_btn.clicked.connect(self.show_archived_management_page)
        self.number_query_btn.clicked.connect(self.show_number_query_page)
        self.test_time_edit_btn.clicked.connect(self.show_test_time_edit_page)
        self.data_mgmt_btn.clicked.connect(self.show_data_management_page)

        sidebar_layout.addWidget(self.import_btn)
        sidebar_layout.addWidget(self.archive_viz_btn)
        sidebar_layout.addWidget(self.transit_data_btn)
        sidebar_layout.addWidget(self.archived_mgmt_btn)
        sidebar_layout.addWidget(self.number_query_btn)
        sidebar_layout.addWidget(self.test_time_edit_btn)
        sidebar_layout.addWidget(self.data_mgmt_btn)

        # 添加弹性空间
        sidebar_layout.addStretch()

        # 状态信息
        status_group = QGroupBox("状态信息")
        status_group.setFont(QFont("Microsoft YaHei", 10))
        status_layout = QVBoxLayout(status_group)

        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        status_layout.addWidget(self.status_label)

        sidebar_layout.addWidget(status_group)

        main_layout.addWidget(sidebar_frame)

    def create_main_content(self, main_layout):
        # 主内容区域使用堆叠窗口
        self.stacked_widget = QStackedWidget()

        # 创建主页面
        self.main_page = QFrame()
        self.main_page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        self.content_layout = QVBoxLayout(self.main_page)
        self.content_layout.setContentsMargins(30, 30, 30, 30)
        self.content_layout.setSpacing(20)

        # 创建月份分析页面
        self.month_analysis_page = self.create_month_analysis_page()

        # 创建归档时长分析页面
        self.archive_analysis_page = self.create_archive_analysis_page()

        # 创建原因分析页面
        self.reason_analysis_page = self.create_reason_analysis_page()

        # 创建区域分析页面
        self.region_analysis_page = self.create_region_analysis_page()

        # 创建归档可视化页面
        self.archive_visualization_page = self.create_archive_visualization_page()

        # 创建在途数据处理页面
        self.transit_data_page = self.create_transit_data_page()

        # 创建归档工单管理页面
        self.archived_management_page = self.create_archived_management_page()

        # 创建号码查询页面
        self.number_query_page = self.create_number_query_page()

        # 创建在途数据详细操作页面
        self.transit_detail_page = self.create_transit_detail_page()

        # 创建流程处理页面
        self.process_pages = {}
        self.create_process_pages()

        # 创建修改现场测试时间页面
        self.test_time_edit_page = self.create_test_time_edit_page()

        # 创建数据管理页面
        self.data_management_page = self.create_data_management_page()

        # 添加页面到堆叠窗口
        self.stacked_widget.addWidget(self.main_page)
        self.stacked_widget.addWidget(self.month_analysis_page)
        self.stacked_widget.addWidget(self.archive_analysis_page)
        self.stacked_widget.addWidget(self.reason_analysis_page)
        self.stacked_widget.addWidget(self.region_analysis_page)
        self.stacked_widget.addWidget(self.archive_visualization_page)
        self.stacked_widget.addWidget(self.transit_data_page)
        self.stacked_widget.addWidget(self.archived_management_page)
        self.stacked_widget.addWidget(self.number_query_page)
        self.stacked_widget.addWidget(self.test_time_edit_page)
        self.stacked_widget.addWidget(self.transit_detail_page)

        # 添加流程处理页面
        for page in self.process_pages.values():
            self.stacked_widget.addWidget(page)

        self.stacked_widget.addWidget(self.data_management_page)

        main_layout.addWidget(self.stacked_widget, 1)

    def clear_content(self):
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget(): # type: ignore
                child.widget().deleteLater() # type: ignore

    def update_sidebar_buttons(self, active_button):
        """统一更新侧边栏按钮状态"""
        # 所有按钮先设为未选中
        self.import_btn.setChecked(False)
        self.archive_viz_btn.setChecked(False)
        self.transit_data_btn.setChecked(False)
        self.number_query_btn.setChecked(False)
        self.test_time_edit_btn.setChecked(False)
        self.data_mgmt_btn.setChecked(False)

        # 设置当前活跃按钮为选中状态
        if active_button:
            active_button.setChecked(True)

    def show_import_page(self):
        # 切换到主页面
        self.stacked_widget.setCurrentWidget(self.main_page)

        # 更新按钮状态
        self.update_sidebar_buttons(self.import_btn)

        # 清空并重新创建内容
        self.clear_content()

        # 文件选择区域
        file_frame = QFrame()
        file_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 2px dashed rgba(102, 126, 234, 0.3);
                border-radius: 15px;
                min-height: 200px;
            }
        """)

        file_layout = QVBoxLayout(file_frame)
        file_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)



        file_text = QLabel("点击选择Excel文件或拖拽文件到此处")
        file_text.setFont(QFont("Microsoft YaHei", 14))
        file_text.setStyleSheet("color: #667eea; margin: 20px;")
        file_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        file_layout.addWidget(file_text)

        # 选择文件按钮
        select_btn = ModernButton("选择文件", True)
        select_btn.clicked.connect(self.select_file)
        file_layout.addWidget(select_btn)

        self.content_layout.addWidget(file_frame)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 10px;
                background: rgba(0, 0, 0, 0.1);
                height: 20px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
            }
        """)
        self.content_layout.addWidget(self.progress_bar)

        # 数据显示区域
        self.create_data_display_area()

        self.content_layout.addStretch()



    def create_data_display_area(self):
        # 数据显示区域
        self.data_group = QGroupBox("数据预览")
        self.data_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.data_group.setVisible(False)

        data_layout = QVBoxLayout(self.data_group)

        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.1);
                background: white;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                font-weight: bold;
                border: none;
                padding: 8px;
            }
        """)
        data_layout.addWidget(self.data_table)

        self.content_layout.addWidget(self.data_group)

    def create_month_analysis_page(self):
        """创建月份分析页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)  # 减小边距
        layout.setSpacing(10)  # 减小间距

        # 页面标题和返回按钮 - 紧凑布局
        header_layout = QHBoxLayout()

        back_btn = ModernButton("← 返回归档可视化")
        back_btn.clicked.connect(self.show_archive_visualization_page)
        back_btn.setFixedWidth(120)
        back_btn.setFixedHeight(35)  # 减小按钮高度
        header_layout.addWidget(back_btn)

        header_layout.addStretch()

        title = QLabel("📅 月份变化分析")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))  # 减小字体
        title.setStyleSheet("color: #333;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 年份选择器 - 参考地图页面设计
        selector_frame = QFrame()
        selector_frame.setFixedHeight(60)  # 固定高度，和地图页面一致
        selector_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 8px;
            }
        """)

        selector_layout = QHBoxLayout(selector_frame)  # 水平布局
        selector_layout.setContentsMargins(10, 5, 10, 5)

        # 左侧：年份选择
        year_label = QLabel("📅 选择年份:")
        year_label.setFont(QFont("Microsoft YaHei", 10))  # 和地图页面一致的字体大小
        year_label.setStyleSheet("color: #667eea;")
        selector_layout.addWidget(year_label)

        self.year_combo = QComboBox()
        self.year_combo.setFixedHeight(35)  # 适中高度
        self.year_combo.setFont(QFont("Microsoft YaHei", 10))  # 和地图页面一致的字体大小
        self.year_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 15px;
                padding: 5px 10px;
                min-width: 100px;
            }
            QComboBox:hover {
                border: 2px solid rgba(102, 126, 234, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
            }
        """)
        self.year_combo.currentTextChanged.connect(self.update_month_chart)
        selector_layout.addWidget(self.year_combo)

        selector_layout.addStretch()

        # 右侧：统计信息 - 和地图页面一样放在同一行
        self.stats_label = QLabel()
        self.stats_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))  # 和地图页面一致
        self.stats_label.setStyleSheet("color: #333;")
        selector_layout.addWidget(self.stats_label)

        layout.addWidget(selector_frame)

        # 图表区域 - 更大空间
        self.chart_frame = QFrame()
        self.chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
            }
        """)

        self.chart_layout = QVBoxLayout(self.chart_frame)
        self.chart_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距给图表更多空间

        layout.addWidget(self.chart_frame, 1)

        return page

    def create_archive_analysis_page(self):
        """创建归档时长分析页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)  # 减小边距
        layout.setSpacing(10)  # 减小间距

        # 页面标题和返回按钮 - 紧凑布局
        header_layout = QHBoxLayout()

        back_btn = ModernButton("← 返回归档可视化")
        back_btn.clicked.connect(self.show_archive_visualization_page)
        back_btn.setFixedWidth(120)
        back_btn.setFixedHeight(35)  # 减小按钮高度
        header_layout.addWidget(back_btn)

        header_layout.addStretch()

        title = QLabel("⏱️ 归档时长分析")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))  # 减小字体
        title.setStyleSheet("color: #333;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 分析说明 - 紧凑布局
        info_frame = QFrame()
        info_frame.setFixedHeight(60)  # 固定高度
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 8px;
            }
        """)

        info_layout = QHBoxLayout(info_frame)  # 改为水平布局
        info_layout.setContentsMargins(10, 5, 10, 5)

        info_label = QLabel("⏱️ 计算K列-J列天数差，'无需现场测试'为1天")
        info_label.setFont(QFont("Microsoft YaHei", 10))  # 减小字体
        info_label.setStyleSheet("color: #667eea;")
        info_layout.addWidget(info_label)

        info_layout.addStretch()

        # 统计信息 - 放在同一行
        self.archive_stats_label = QLabel()
        self.archive_stats_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        self.archive_stats_label.setStyleSheet("color: #333;")
        info_layout.addWidget(self.archive_stats_label)

        layout.addWidget(info_frame)

        # 图表区域 - 更大空间
        self.archive_chart_frame = QFrame()
        self.archive_chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
            }
        """)

        self.archive_chart_layout = QVBoxLayout(self.archive_chart_frame)
        self.archive_chart_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距给图表更多空间

        layout.addWidget(self.archive_chart_frame, 1)

        return page

    def create_reason_analysis_page(self):
        """创建原因分析页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)  # 减小边距
        layout.setSpacing(10)  # 减小间距

        # 页面标题和返回按钮 - 紧凑布局
        header_layout = QHBoxLayout()

        back_btn = ModernButton("← 返回归档可视化")
        back_btn.clicked.connect(self.show_archive_visualization_page)
        back_btn.setFixedWidth(120)
        back_btn.setFixedHeight(35)  # 减小按钮高度
        header_layout.addWidget(back_btn)

        header_layout.addStretch()

        title = QLabel("🔍 原因分析")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))  # 减小字体
        title.setStyleSheet("color: #333;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 分析说明 - 紧凑布局
        info_frame = QFrame()
        info_frame.setFixedHeight(60)  # 固定高度
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 8px;
            }
        """)

        info_layout = QHBoxLayout(info_frame)  # 改为水平布局
        info_layout.setContentsMargins(10, 5, 10, 5)

        info_label = QLabel("🔍 读取P列开单原因数据，聚类分析统计频次")
        info_label.setFont(QFont("Microsoft YaHei", 10))  # 减小字体
        info_label.setStyleSheet("color: #667eea;")
        info_layout.addWidget(info_label)

        info_layout.addStretch()

        # 统计信息 - 放在同一行
        self.reason_stats_label = QLabel()
        self.reason_stats_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        self.reason_stats_label.setStyleSheet("color: #333;")
        info_layout.addWidget(self.reason_stats_label)

        layout.addWidget(info_frame)

        # 图表切换控制区域
        control_frame = QFrame()
        control_frame.setFixedHeight(50)
        control_frame.setStyleSheet("""
            QFrame {
                background: rgba(102, 126, 234, 0.05);
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 5px;
            }
        """)

        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 5, 10, 5)

        # 上一个按钮
        self.prev_chart_btn = ModernButton("← 上一个图表")
        self.prev_chart_btn.setFixedWidth(120)
        self.prev_chart_btn.setFixedHeight(35)
        self.prev_chart_btn.clicked.connect(self.show_previous_reason_chart)
        control_layout.addWidget(self.prev_chart_btn)

        control_layout.addStretch()

        # 图表标题
        self.chart_title_label = QLabel("饼图 (1/2)")
        self.chart_title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.chart_title_label.setStyleSheet("color: #667eea;")
        self.chart_title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        control_layout.addWidget(self.chart_title_label)

        control_layout.addStretch()

        # 下一个按钮
        self.next_chart_btn = ModernButton("下一个图表 →")
        self.next_chart_btn.setFixedWidth(120)
        self.next_chart_btn.setFixedHeight(35)
        self.next_chart_btn.clicked.connect(self.show_next_reason_chart)
        control_layout.addWidget(self.next_chart_btn)

        layout.addWidget(control_frame)

        # 图表区域 - 单图表显示，更大空间
        charts_frame = QFrame()
        charts_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
            }
        """)

        # 使用垂直布局显示单个图表
        self.reason_chart_layout = QVBoxLayout(charts_frame)
        self.reason_chart_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距给图表更多空间

        # 初始化图表索引
        self.current_chart_index = 0
        self.reason_charts = []  # 存储图表canvas

        layout.addWidget(charts_frame, 1)

        return page

    def show_previous_reason_chart(self):
        """显示上一个原因分析图表"""
        if hasattr(self, 'reason_charts') and self.reason_charts:
            self.current_chart_index = (self.current_chart_index - 1) % len(self.reason_charts)
            self.update_reason_chart_display()

    def show_next_reason_chart(self):
        """显示下一个原因分析图表"""
        if hasattr(self, 'reason_charts') and self.reason_charts:
            self.current_chart_index = (self.current_chart_index + 1) % len(self.reason_charts)
            self.update_reason_chart_display()

    def update_reason_chart_display(self):
        """更新原因分析图表显示"""
        if not hasattr(self, 'reason_charts') or not self.reason_charts:
            return

        # 清除当前显示的图表
        for i in reversed(range(self.reason_chart_layout.count())):
            item = self.reason_chart_layout.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None) # type: ignore

        # 显示当前选中的图表
        current_canvas = self.reason_charts[self.current_chart_index]
        self.reason_chart_layout.addWidget(current_canvas)

        # 更新标题
        chart_names = ["饼图", "柱状图"]
        if self.current_chart_index < len(chart_names):
            chart_name = chart_names[self.current_chart_index]
            self.chart_title_label.setText(f"{chart_name} ({self.current_chart_index + 1}/{len(self.reason_charts)})")

        # 更新按钮状态
        self.prev_chart_btn.setEnabled(len(self.reason_charts) > 1)
        self.next_chart_btn.setEnabled(len(self.reason_charts) > 1)

    def create_region_analysis_page(self):
        """创建区域分析页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)  # 减小边距
        layout.setSpacing(10)  # 减小间距

        # 页面标题和返回按钮 - 紧凑布局
        header_layout = QHBoxLayout()

        back_btn = ModernButton("← 返回归档可视化")
        back_btn.clicked.connect(self.show_archive_visualization_page)
        back_btn.setFixedWidth(120)
        back_btn.setFixedHeight(35)  # 减小按钮高度
        header_layout.addWidget(back_btn)

        header_layout.addStretch()

        title = QLabel("🌍 区域分析")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))  # 减小字体
        title.setStyleSheet("color: #333;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 年份选择器 - 参考月份分析页面设计
        selector_frame = QFrame()
        selector_frame.setFixedHeight(60)  # 固定高度，和月份页面一致
        selector_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 8px;
            }
        """)

        selector_layout = QHBoxLayout(selector_frame)  # 水平布局
        selector_layout.setContentsMargins(10, 5, 10, 5)

        # 左侧：说明信息
        info_label = QLabel("📍 读取M/N列经纬度数据，在云南省地图上显示工单分布位置")
        info_label.setFont(QFont("Microsoft YaHei", 10))  # 和月份页面一致的字体大小
        info_label.setStyleSheet("color: #667eea;")
        selector_layout.addWidget(info_label)

        selector_layout.addStretch()

        # 中间：年份选择
        year_label = QLabel("📅 选择年份:")
        year_label.setFont(QFont("Microsoft YaHei", 10))  # 和月份页面一致的字体大小
        year_label.setStyleSheet("color: #667eea;")
        selector_layout.addWidget(year_label)

        self.region_year_combo = QComboBox()
        self.region_year_combo.setFixedHeight(35)  # 适中高度
        self.region_year_combo.setFont(QFont("Microsoft YaHei", 10))  # 和月份页面一致的字体大小
        self.region_year_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 15px;
                padding: 5px 10px;
                min-width: 100px;
            }
            QComboBox:hover {
                border: 2px solid rgba(102, 126, 234, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
            }
        """)
        self.region_year_combo.currentTextChanged.connect(self.update_region_chart)
        selector_layout.addWidget(self.region_year_combo)

        selector_layout.addStretch()

        # 右侧：统计信息 - 和月份页面一样放在同一行
        self.region_stats_label = QLabel()
        self.region_stats_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))  # 和月份页面一致
        self.region_stats_label.setStyleSheet("color: #333;")
        selector_layout.addWidget(self.region_stats_label)

        layout.addWidget(selector_frame)

        # 地图控制按钮区域
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background: rgba(102, 126, 234, 0.05);
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 8px;
                padding: 8px;
            }
        """)

        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 5, 10, 5)

        # 地图类型选择
        map_type_label = QLabel("🗺️ 地图类型:")
        map_type_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        control_layout.addWidget(map_type_label)

        self.map_type_combo = QComboBox()
        self.map_type_combo.addItems(["交互式地图", "静态地图"])
        self.map_type_combo.setFixedWidth(120)
        self.map_type_combo.currentTextChanged.connect(self.on_map_type_changed)
        control_layout.addWidget(self.map_type_combo)

        control_layout.addStretch()

        # 刷新按钮
        refresh_btn = ModernButton("🔄 刷新地图")
        refresh_btn.setFixedSize(100, 30)
        refresh_btn.clicked.connect(self.refresh_region_map)
        control_layout.addWidget(refresh_btn)

        layout.addWidget(control_frame)

        # 图表区域 - 支持交互式和静态地图
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 10px;
            }
        """)

        self.region_chart_layout = QVBoxLayout(chart_frame)
        self.region_chart_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距

        layout.addWidget(chart_frame, 1)

        return page

    def on_map_type_changed(self, map_type):
        """地图类型改变时的处理"""
        if hasattr(self, 'current_coordinates_by_year') and self.current_coordinates_by_year:
            year_str = self.region_year_combo.currentText()
            if year_str:
                try:
                    year_int = int(year_str)
                    if year_int in self.current_coordinates_by_year:
                        coordinates = self.current_coordinates_by_year[year_int]
                        if map_type == "交互式地图":
                            self.create_interactive_map(coordinates, 0)
                        else:
                            self.create_region_analysis_chart(coordinates, 0)
                except ValueError:
                    print(f"无效的年份格式: {year_str}")

    def refresh_region_map(self):
        """刷新地图"""
        if hasattr(self, 'current_coordinates_by_year') and self.current_coordinates_by_year:
            year_str = self.region_year_combo.currentText()
            if year_str:
                try:
                    year_int = int(year_str)
                    if year_int in self.current_coordinates_by_year:
                        coordinates = self.current_coordinates_by_year[year_int]
                        map_type = self.map_type_combo.currentText()
                        if map_type == "交互式地图":
                            self.create_interactive_map(coordinates, 0)
                        else:
                            self.create_region_analysis_chart(coordinates, 0)
                except ValueError:
                    print(f"无效的年份格式: {year_str}")

    def update_region_chart(self, year_str):
        """更新区域图表 - 根据选择的年份显示对应数据"""
        if not year_str or not hasattr(self, 'current_coordinates_by_year'):
            return

        try:
            # 将字符串年份转换为整数进行查找
            year_int = int(year_str)
            if year_int in self.current_coordinates_by_year:
                coordinates = self.current_coordinates_by_year[year_int]

                # 根据当前选择的地图类型生成图表
                map_type = getattr(self, 'map_type_combo', None)
                if map_type and map_type.currentText() == "交互式地图":
                    self.create_interactive_map(coordinates, 0)
                else:
                    self.create_region_analysis_chart(coordinates, 0)

                print(f"区域图表已更新为{year_str}年数据，包含{len(coordinates)}个坐标点")
            else:
                print(f"没有找到{year_str}年的数据")
                print(f"可用年份: {list(self.current_coordinates_by_year.keys())}")

        except ValueError:
            print(f"无效的年份格式: {year_str}")
        except Exception as e:
            error_msg = f"更新区域图表时出错: {str(e)}"
            print(error_msg)
            QMessageBox.warning(self, "警告", error_msg)

    def create_archive_visualization_page(self):
        """创建归档可视化页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("📊 归档可视化")
        title.setFont(QFont("Microsoft YaHei", 24, QFont.Weight.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 数据分析功能按钮区域
        self.create_archive_analysis_buttons(layout)

        return page

    def create_transit_data_page(self):
        """创建在途数据处理页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("🚛 在途数据处理")
        title.setFont(QFont("Microsoft YaHei", 24, QFont.Weight.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 功能说明
        info_frame = QFrame()
        info_frame.setFixedHeight(60)
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 8px;
            }
        """)

        info_layout = QHBoxLayout(info_frame)
        info_layout.setContentsMargins(10, 5, 10, 5)

        info_label = QLabel("🚛 筛选并修改Q列字段为空或'在途'状态的工单数据")
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("color: #667eea;")
        info_layout.addWidget(info_label)

        info_layout.addStretch()

        # 统计信息
        self.transit_stats_label = QLabel()
        self.transit_stats_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        self.transit_stats_label.setStyleSheet("color: #333;")
        info_layout.addWidget(self.transit_stats_label)

        layout.addWidget(info_frame)

        # 数据概览卡片
        overview_frame = QFrame()
        overview_frame.setFixedHeight(100)
        overview_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                border: none;
            }
        """)

        overview_layout = QHBoxLayout(overview_frame)
        overview_layout.setContentsMargins(25, 20, 25, 20)

        # 左侧图标
        overview_icon = QLabel("📊")
        overview_icon.setFont(QFont("Microsoft YaHei", 24))
        overview_layout.addWidget(overview_icon)

        # 中间统计信息
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(5)

        stats_title = QLabel("数据统计")
        stats_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        stats_title.setStyleSheet("color: white;")
        stats_layout.addWidget(stats_title)

        self.process_stats_label = QLabel("正在加载数据...")
        self.process_stats_label.setFont(QFont("Microsoft YaHei", 11))
        self.process_stats_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        stats_layout.addWidget(self.process_stats_label)

        overview_layout.addLayout(stats_layout)
        overview_layout.addStretch()

        # 右侧刷新按钮
        refresh_btn = ModernButton("🔄 刷新")
        refresh_btn.setFixedWidth(80)
        refresh_btn.setFixedHeight(35)
        refresh_btn.clicked.connect(self.update_transit_stats)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 17px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        """)
        overview_layout.addWidget(refresh_btn)

        layout.addWidget(overview_frame)

        # 在途数据预览区域
        preview_frame = QFrame()
        preview_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 15px;
            }
        """)

        preview_layout = QVBoxLayout(preview_frame)
        preview_layout.setContentsMargins(20, 15, 20, 15)
        preview_layout.setSpacing(15)

        # 预览标题栏
        preview_header = QHBoxLayout()

        preview_icon = QLabel("📋")
        preview_icon.setFont(QFont("Microsoft YaHei", 16))
        preview_header.addWidget(preview_icon)

        preview_title = QLabel("在途数据预览")
        preview_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        preview_title.setStyleSheet("color: #495057; margin-left: 8px;")
        preview_header.addWidget(preview_title)

        preview_header.addStretch()

        # 刷新数据按钮
        refresh_data_btn = ModernButton("🔄 刷新数据")
        refresh_data_btn.setFixedWidth(90)
        refresh_data_btn.setFixedHeight(30)
        refresh_data_btn.clicked.connect(self.refresh_transit_preview_data)
        preview_header.addWidget(refresh_data_btn)

        preview_layout.addLayout(preview_header)

        # 在途数据表格
        self.transit_table = QTableWidget()
        self.transit_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #e9ecef;
                border-radius: 10px;
                background: white;
                gridline-color: #f0f0f0;
                selection-background-color: rgba(102, 126, 234, 0.2);
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f8f9fa;
            }
            QTableWidget::item:selected {
                background: rgba(102, 126, 234, 0.3);
                color: #333;
            }
            QTableWidget::item:hover {
                background: rgba(102, 126, 234, 0.1);
            }
            QHeaderView::section {
                background: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #e9ecef;
                font-weight: bold;
                color: #495057;
            }
        """)

        self.transit_table.setAlternatingRowColors(True)
        self.transit_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.transit_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # 双击事件
        self.transit_table.itemDoubleClicked.connect(self.show_transit_detail_page)

        # 右键菜单
        self.transit_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.transit_table.customContextMenuRequested.connect(self.show_transit_context_menu)

        preview_layout.addWidget(self.transit_table)

        # 操作提示
        tip_label = QLabel("💡 双击任意在途数据行进入详细操作页面")
        tip_label.setFont(QFont("Microsoft YaHei", 10))
        tip_label.setStyleSheet("""
            color: #6c757d;
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
        """)
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(tip_label)

        layout.addWidget(preview_frame, 1)

        return page



    def create_transit_detail_page(self):
        """创建在途数据详细操作页面"""
        # 创建主容器
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(0, 0, 0, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: rgba(102, 126, 234, 0.5);
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(102, 126, 234, 0.7);
            }
        """)

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)  # 减小边距
        layout.setSpacing(15)  # 减小间距

        # 将内容容器设置到滚动区域
        scroll_area.setWidget(content_widget)

        # 创建页面主布局
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.addWidget(scroll_area)

        # 页面标题和返回按钮 - 更紧凑
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)

        back_btn = ModernButton("← 返回在途数据处理")
        back_btn.clicked.connect(self.show_transit_data_page)
        back_btn.setFixedSize(120, 35)  # 减小高度
        header_layout.addWidget(back_btn)

        header_layout.addStretch()

        title = QLabel("🔧 在途数据详细操作")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))  # 减小字体
        title.setStyleSheet("color: #2c3e50;")
        title.setFixedHeight(35)  # 固定高度
        header_layout.addWidget(title)

        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 数据信息卡片 - 更紧凑
        info_frame = QFrame()
        info_frame.setFixedHeight(80)  # 减小高度
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                border: none;
            }
        """)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(15, 8, 15, 8)  # 减小边距
        info_layout.setSpacing(5)  # 减小间距

        # 数据标题
        self.detail_title_label = QLabel("选中的在途数据信息")
        self.detail_title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))  # 减小字体
        self.detail_title_label.setStyleSheet("color: white;")
        info_layout.addWidget(self.detail_title_label)

        # 数据详情
        self.detail_info_label = QLabel("请从处理中心选择一条在途数据")
        self.detail_info_label.setFont(QFont("Microsoft YaHei", 10))  # 减小字体
        self.detail_info_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        self.detail_info_label.setWordWrap(True)
        info_layout.addWidget(self.detail_info_label)

        layout.addWidget(info_frame)

        # 操作区域 - 更紧凑
        operation_frame = QFrame()
        operation_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 10px;
            }
        """)

        operation_layout = QVBoxLayout(operation_frame)
        operation_layout.setContentsMargins(15, 12, 15, 12)  # 减小边距
        operation_layout.setSpacing(12)  # 减小间距

        # 操作标题 - 更紧凑
        operation_title_layout = QHBoxLayout()
        operation_title_layout.setContentsMargins(0, 0, 0, 0)

        operation_icon = QLabel("🛠️")
        operation_icon.setFont(QFont("Microsoft YaHei", 14))  # 减小图标
        operation_title_layout.addWidget(operation_icon)

        operation_title = QLabel("可执行操作")
        operation_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 减小字体
        operation_title.setStyleSheet("color: #495057; margin-left: 8px;")
        operation_title.setFixedHeight(25)  # 固定高度
        operation_title_layout.addWidget(operation_title)

        operation_title_layout.addStretch()

        operation_layout.addLayout(operation_title_layout)

        # 流程选择区域 - 更紧凑
        process_selection_frame = QFrame()
        process_selection_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        process_layout = QVBoxLayout(process_selection_frame)
        process_layout.setSpacing(10)  # 减小间距

        # 流程选择标题 - 更紧凑
        process_title_layout = QHBoxLayout()
        process_title_layout.setContentsMargins(0, 0, 0, 0)

        process_icon = QLabel("📋")
        process_icon.setFont(QFont("Microsoft YaHei", 16))  # 增大图标以匹配标题
        process_icon.setMinimumHeight(30)  # 与标题保持一致的高度
        process_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        process_title_layout.addWidget(process_icon)

        process_title = QLabel("选择工单需要经过的流程")
        process_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))  # 增大字体
        process_title.setStyleSheet("color: #495057; margin-left: 8px; padding: 4px 0px;")
        process_title.setMinimumHeight(30)  # 设置最小高度而不是固定高度
        process_title.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        process_title_layout.addWidget(process_title)

        process_title_layout.addStretch()

        process_layout.addLayout(process_title_layout)

        # 流程说明 - 更紧凑
        process_info = QLabel("💡 请选择此工单需要经过的流程步骤，并为每个流程选择对应的原因。选中的流程将按顺序执行：规划 → 建设 → 维护 → 优化 → 客户。注意：除客户流程外，其他流程都需要选择具体原因。")
        process_info.setFont(QFont("Microsoft YaHei", 9))  # 减小字体
        process_info.setStyleSheet("""
            color: #6c757d;
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 6px;
            padding: 8px;
        """)
        process_info.setWordWrap(True)
        process_info.setMaximumHeight(60)  # 限制最大高度
        process_layout.addWidget(process_info)

        # 流程选择复选框区域 - 更紧凑
        checkbox_frame = QFrame()
        checkbox_frame.setStyleSheet("""
            QFrame {
                background: rgba(248, 249, 250, 0.8);
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
            }
        """)

        checkbox_layout = QGridLayout(checkbox_frame)
        checkbox_layout.setSpacing(8)  # 减小间距
        checkbox_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距

        # 创建五个流程复选框和下拉框
        self.process_checkboxes = {}
        self.process_reason_combos = {}

        # 定义原因选项和对应天数
        self.reason_options = {
            'planning': {
                '非大网': 7,
                '大网': 10
            },
            'construction': {
                '催开已规划待落地--覆盖类小区拉远(覆盖类)': 90,
                '催开已规划待落地--覆盖类室分(覆盖类)': 180,
                '催开已规划待落地--覆盖类新址新建楼面塔(覆盖类)': 50,
                '催开已规划待落地--容量类小区拉远(容量类)': 90,
                '催开已规划待落地--覆盖类新址新建地面塔(覆盖类)': 90,
                '催开已规划待落地--容量类室分(容量类)': 180,
                '催开已规划待落地--容量类新址新建地面塔(容量类)': 90,
                '催开已规划待落地--覆盖类室分(覆盖类)': 30
            },
            'maintenance': {
                '故障电力故障电力故障': 3,
                '故障基站故障基站故障': 3,
                '故障直放站\\室分\\网优产品等故障直放站\\室分\\网优产品等故障': 3,
                '故障传输故障传输故障': 3,
                '物业无法上站长期不在家': 30,
                '维护利旧-拆闲补忙/补盲（成本类）小区拉远补盲/忙': 60,
                '维护站址搬迁基站拆除': 30,
                '维护站址搬迁基站搬迁': 60,
                '维护主设备整改基带单元整治': 30,
                '维护天馈整改室分-分布系统整改': 30,
                '维护站址搬迁基站拆除': 30,
                '维护天馈整改室分-延伸覆盖': 30,
                '维护天馈整改抱杆整治（自维站点）': 30,
                '维护天馈整改天馈整治': 30,
                '维护配套整改动环改造': 60,
                '维护主设备整改射频单元整治': 30,
                '维护主设备整改': 30,
                '维护天馈整改无源器件整治': 30,
                '维护天馈整改美化罩整治': 30,
                '维护配套整改传输改造': 60,
                '维护利旧-拆闲补忙/补盲（成本类）': 30,
                '物业排斥入场竞对排他': 90,
                '物业无法上站无法联系上': 30,
                '物业排斥入场居民闹辐射': 90,
                '物业排斥入场竞对排他': 90,
                '物业费用纠纷电费': 30,
                '物业费用纠纷其他费用': 30,
                '物业费用纠纷租费': 30,
                '物业特殊区域党政机关': 30
            },
            'optimization': {
                '优化后台优化其他(后台优化)': 30,
                '优化三新运用应急宝部署': 14,
                '优化三新运用满格宝/femto': 14,
                '优化前台优化站间联调': 7,
                '优化前台优化本扇区调整': 3,
                '优化前台优化小区合并分裂': 14,
                '优化前台优化干扰协调': 14,
                '优化后台优化退网处理': 30,
                '优化前台优化本站调整': 3,
                '优化后台优化软件版本问题': 7,
                '优化后台优化业务突增': 14,
                '优化三新运用载频调度': 3,
                '优化后台优化参数调整优化': 3,
                '优化前台优化本小区调整': 3,
                '优化后台优化邻区问题整治': 3,
                '代维天馈调整抱杆天馈调整': 4
            },
            'customer': {
                '现场测试': 3,
                '客户沟通': 2
            }
        }

        # 规划 - 更紧凑
        planning_container = QWidget()
        planning_container.setFixedHeight(35)  # 固定高度
        planning_layout = QHBoxLayout(planning_container)
        planning_layout.setContentsMargins(0, 0, 0, 0)
        planning_layout.setSpacing(8)  # 减小间距

        self.planning_checkbox = QCheckBox("📊 规划")
        self.planning_checkbox.setFont(QFont("Microsoft YaHei", 10))  # 减小字体
        self.planning_checkbox.setStyleSheet("""
            QCheckBox {
                color: #495057;
                spacing: 6px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #667eea;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #667eea;
                border: 2px solid #667eea;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        self.process_checkboxes['planning'] = self.planning_checkbox
        planning_layout.addWidget(self.planning_checkbox)

        # 规划原因下拉框 - 更紧凑
        self.planning_reason_combo = QComboBox()
        self.planning_reason_combo.setFixedHeight(25)  # 减小高度
        self.planning_reason_combo.setFixedWidth(180)  # 减小宽度
        self.planning_reason_combo.setFont(QFont("Microsoft YaHei", 8))  # 减小字体
        self.planning_reason_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 8px;
                padding: 3px 8px;
            }
            QComboBox:hover {
                border: 1px solid rgba(102, 126, 234, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.planning_reason_combo.addItems(['请选择原因'] + list(self.reason_options['planning'].keys()))
        self.process_reason_combos['planning'] = self.planning_reason_combo
        planning_layout.addWidget(self.planning_reason_combo)
        planning_layout.addStretch()

        checkbox_layout.addWidget(planning_container, 0, 0)

        # 建设
        construction_container = QWidget()
        construction_layout = QHBoxLayout(construction_container)
        construction_layout.setContentsMargins(0, 0, 0, 0)
        construction_layout.setSpacing(10)

        self.construction_checkbox = QCheckBox("🏗️ 建设")
        self.construction_checkbox.setFont(QFont("Microsoft YaHei", 12))
        self.construction_checkbox.setStyleSheet("""
            QCheckBox {
                color: #495057;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #28a745;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #28a745;
                border: 2px solid #28a745;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        self.process_checkboxes['construction'] = self.construction_checkbox
        construction_layout.addWidget(self.construction_checkbox)

        # 建设原因下拉框
        self.construction_reason_combo = QComboBox()
        self.construction_reason_combo.setFixedHeight(30)
        self.construction_reason_combo.setFixedWidth(200)
        self.construction_reason_combo.setFont(QFont("Microsoft YaHei", 9))
        self.construction_reason_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 1px solid rgba(40, 167, 69, 0.3);
                border-radius: 8px;
                padding: 3px 8px;
            }
            QComboBox:hover {
                border: 1px solid rgba(40, 167, 69, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.construction_reason_combo.addItems(['请选择原因'] + list(self.reason_options['construction'].keys()))
        self.process_reason_combos['construction'] = self.construction_reason_combo
        construction_layout.addWidget(self.construction_reason_combo)
        construction_layout.addStretch()

        checkbox_layout.addWidget(construction_container, 0, 1)

        # 维护
        maintenance_container = QWidget()
        maintenance_layout = QHBoxLayout(maintenance_container)
        maintenance_layout.setContentsMargins(0, 0, 0, 0)
        maintenance_layout.setSpacing(10)

        self.maintenance_checkbox = QCheckBox("🔧 维护")
        self.maintenance_checkbox.setFont(QFont("Microsoft YaHei", 12))
        self.maintenance_checkbox.setStyleSheet("""
            QCheckBox {
                color: #495057;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #ffc107;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #ffc107;
                border: 2px solid #ffc107;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        self.process_checkboxes['maintenance'] = self.maintenance_checkbox
        maintenance_layout.addWidget(self.maintenance_checkbox)

        # 维护原因下拉框
        self.maintenance_reason_combo = QComboBox()
        self.maintenance_reason_combo.setFixedHeight(30)
        self.maintenance_reason_combo.setFixedWidth(200)
        self.maintenance_reason_combo.setFont(QFont("Microsoft YaHei", 9))
        self.maintenance_reason_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 1px solid rgba(255, 193, 7, 0.3);
                border-radius: 8px;
                padding: 3px 8px;
            }
            QComboBox:hover {
                border: 1px solid rgba(255, 193, 7, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.maintenance_reason_combo.addItems(['请选择原因'] + list(self.reason_options['maintenance'].keys()))
        self.process_reason_combos['maintenance'] = self.maintenance_reason_combo
        maintenance_layout.addWidget(self.maintenance_reason_combo)
        maintenance_layout.addStretch()

        checkbox_layout.addWidget(maintenance_container, 1, 0)

        # 优化
        optimization_container = QWidget()
        optimization_layout = QHBoxLayout(optimization_container)
        optimization_layout.setContentsMargins(0, 0, 0, 0)
        optimization_layout.setSpacing(10)

        self.optimization_checkbox = QCheckBox("⚡ 优化")
        self.optimization_checkbox.setFont(QFont("Microsoft YaHei", 12))
        self.optimization_checkbox.setStyleSheet("""
            QCheckBox {
                color: #495057;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #17a2b8;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #17a2b8;
                border: 2px solid #17a2b8;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        self.process_checkboxes['optimization'] = self.optimization_checkbox
        optimization_layout.addWidget(self.optimization_checkbox)

        # 优化原因下拉框 - 暂时为空，用户要求先加三个
        self.optimization_reason_combo = QComboBox()
        self.optimization_reason_combo.setFixedHeight(30)
        self.optimization_reason_combo.setFixedWidth(200)
        self.optimization_reason_combo.setFont(QFont("Microsoft YaHei", 9))
        self.optimization_reason_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 1px solid rgba(23, 162, 184, 0.3);
                border-radius: 8px;
                padding: 3px 8px;
            }
            QComboBox:hover {
                border: 1px solid rgba(23, 162, 184, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.optimization_reason_combo.addItems(['请选择原因'] + list(self.reason_options['optimization'].keys()))
        self.process_reason_combos['optimization'] = self.optimization_reason_combo
        optimization_layout.addWidget(self.optimization_reason_combo)
        optimization_layout.addStretch()

        checkbox_layout.addWidget(optimization_container, 1, 1)

        # 客户 - 不添加下拉框
        customer_container = QWidget()
        customer_layout = QHBoxLayout(customer_container)
        customer_layout.setContentsMargins(0, 0, 0, 0)
        customer_layout.setSpacing(10)

        self.customer_checkbox = QCheckBox("👥 客户")
        self.customer_checkbox.setFont(QFont("Microsoft YaHei", 12))
        self.customer_checkbox.setStyleSheet("""
            QCheckBox {
                color: #495057;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #dc3545;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #dc3545;
                border: 2px solid #dc3545;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        self.process_checkboxes['customer'] = self.customer_checkbox
        customer_layout.addWidget(self.customer_checkbox)

        # 客户原因下拉框
        self.customer_reason_combo = QComboBox()
        self.customer_reason_combo.setFixedHeight(30)
        self.customer_reason_combo.setFixedWidth(200)
        self.customer_reason_combo.setFont(QFont("Microsoft YaHei", 9))
        self.customer_reason_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 1px solid rgba(220, 53, 69, 0.3);
                border-radius: 8px;
                padding: 3px 8px;
            }
            QComboBox:hover {
                border: 1px solid rgba(220, 53, 69, 0.6);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.customer_reason_combo.addItems(['请选择原因'] + list(self.reason_options['customer'].keys()))
        self.process_reason_combos['customer'] = self.customer_reason_combo
        customer_layout.addWidget(self.customer_reason_combo)
        customer_layout.addStretch()

        checkbox_layout.addWidget(customer_container, 2, 0, 1, 2)  # 跨两列居中

        process_layout.addWidget(checkbox_frame)

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 全选按钮
        select_all_btn = ModernButton("✅ 全选流程")
        select_all_btn.setFixedWidth(120)
        select_all_btn.setFixedHeight(40)
        select_all_btn.clicked.connect(self.select_all_processes)
        button_layout.addWidget(select_all_btn)

        # 清空选择按钮
        clear_all_btn = ModernButton("❌ 清空选择")
        clear_all_btn.setFixedWidth(120)
        clear_all_btn.setFixedHeight(40)
        clear_all_btn.clicked.connect(self.clear_all_processes)
        button_layout.addWidget(clear_all_btn)

        button_layout.addStretch()

        # 确认流程按钮
        confirm_process_btn = ModernButton("🚀 确认流程设置", True)
        confirm_process_btn.setFixedWidth(150)
        confirm_process_btn.setFixedHeight(40)
        confirm_process_btn.clicked.connect(self.confirm_process_selection)
        button_layout.addWidget(confirm_process_btn)

        process_layout.addLayout(button_layout)

        operation_layout.addWidget(process_selection_frame)
        layout.addWidget(operation_frame, 1)

        return page

    def select_all_processes(self):
        """全选所有流程"""
        for checkbox in self.process_checkboxes.values():
            checkbox.setChecked(True)

    def clear_all_processes(self):
        """清空所有流程选择"""
        for checkbox in self.process_checkboxes.values():
            checkbox.setChecked(False)

    def confirm_process_selection(self):
        """确认流程选择"""
        selected_processes = []
        selected_details = []
        process_names = {
            'planning': '规划',
            'construction': '建设',
            'maintenance': '维护',
            'optimization': '优化',
            'customer': '客户'
        }

        # 按顺序检查选中的流程
        process_order = ['planning', 'construction', 'maintenance', 'optimization', 'customer']
        for process_key in process_order:
            if self.process_checkboxes[process_key].isChecked():
                process_name = process_names[process_key]
                selected_processes.append(process_name)

                # 获取原因和天数（所有流程都需要选择原因）
                if process_key in self.process_reason_combos:
                    combo = self.process_reason_combos[process_key]
                    selected_reason = combo.currentText()

                    if selected_reason != '请选择原因':
                        # 获取对应的天数
                        days = self.reason_options[process_key].get(selected_reason, 0)
                        selected_details.append(f"{process_name}: {selected_reason} ({days}天)")
                    else:
                        selected_details.append(f"{process_name}: 未选择原因")
                else:
                    selected_details.append(f"{process_name}: 无需选择原因")

        if not selected_processes:
            QMessageBox.warning(self, "提示", "请至少选择一个流程！")
            return

        # 检查是否有未选择原因的流程（所有流程都需要选择原因）
        missing_reasons = []
        for process_key in process_order:
            if (self.process_checkboxes[process_key].isChecked() and
                process_key in self.process_reason_combos):
                combo = self.process_reason_combos[process_key]
                if combo.currentText() == '请选择原因':
                    missing_reasons.append(process_names[process_key])

        if missing_reasons:
            QMessageBox.warning(self, "提示",
                               f"请为以下流程选择原因：\n{', '.join(missing_reasons)}")
            return

        # 显示选择的流程和详细信息
        process_text = " → ".join(selected_processes)
        details_text = "\n".join(selected_details)

        QMessageBox.information(self, "流程确认",
                               f"已选择流程：\n{process_text}\n\n详细信息：\n{details_text}\n\n流程将按此顺序依次执行。")

        # 保存选择的流程信息
        self.selected_processes = selected_processes
        self.selected_process_details = selected_details
        self.current_process_index = 0

        # 保存流程进度到数据库
        if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
            self.data_manager.save_process_progress(
                self.current_work_order_id,
                selected_processes,
                selected_details,
                0,  # 初始进度为0
                {}  # 初始结果为空
            )

        # 开始进入流程处理
        self.start_process_handling()

    def create_archived_management_page(self):
        """创建归档工单管理页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("📋 归档工单管理")
        title.setFont(QFont("Microsoft YaHei", 24, QFont.Weight.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 功能说明
        info_frame = QFrame()
        info_frame.setFixedHeight(60)
        info_frame.setStyleSheet("""
            QFrame {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 15px;
                border: none;
            }
        """)
        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(20, 10, 20, 10)

        info_label = QLabel("管理已归档的工单，支持查看归档历史和执行回退操作")
        info_label.setFont(QFont("Microsoft YaHei", 12))
        info_label.setStyleSheet("color: white; font-weight: bold;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_layout.addWidget(info_label)

        layout.addWidget(info_frame)

        # 操作按钮区域
        button_frame = QFrame()
        button_frame.setFixedHeight(60)
        button_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(15, 10, 15, 10)

        # 刷新按钮
        refresh_btn = ModernButton("🔄 刷新数据", True)
        refresh_btn.setFixedSize(120, 40)
        refresh_btn.clicked.connect(self.refresh_archived_data)
        button_layout.addWidget(refresh_btn)

        button_layout.addStretch()

        # 统计信息标签
        self.archived_stats_label = QLabel("暂无数据")
        self.archived_stats_label.setFont(QFont("Microsoft YaHei", 11))
        self.archived_stats_label.setStyleSheet("color: #666; font-weight: bold;")
        button_layout.addWidget(self.archived_stats_label)

        layout.addWidget(button_frame)

        # 数据表格
        self.archived_table = QTableWidget()
        self.archived_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background: #f5f5f5;
                border: none;
                padding: 10px;
                font-weight: bold;
                color: #333;
            }
        """)

        # 设置表格列
        self.archived_table.setColumnCount(6)
        self.archived_table.setHorizontalHeaderLabels([
            "工单号", "A列", "C列", "归档时间", "Excel行号", "状态"
        ])

        # 设置列宽
        header = self.archived_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 工单号
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # A列
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # C列
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 归档时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # Excel行号
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 状态

        self.archived_table.setColumnWidth(0, 120)  # 工单号
        self.archived_table.setColumnWidth(3, 150)  # 归档时间
        self.archived_table.setColumnWidth(4, 80)   # Excel行号
        self.archived_table.setColumnWidth(5, 80)   # 状态

        # 设置表格属性
        self.archived_table.setAlternatingRowColors(True)
        self.archived_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.archived_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.archived_table.customContextMenuRequested.connect(self.show_archived_context_menu)

        layout.addWidget(self.archived_table)

        return page

    def create_number_query_page(self):
        """创建号码次数查询页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 20px;
                border: none;
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 页面标题区域 - 优化高度和布局
        title_card = QFrame()
        title_card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                border: none;
            }
        """)
        title_card.setFixedHeight(75)

        title_layout = QHBoxLayout(title_card)
        title_layout.setContentsMargins(20, 10, 20, 10)
        title_layout.setSpacing(15)

        # 左侧图标和标题
        title_left_layout = QVBoxLayout()
        title_left_layout.setSpacing(2)

        title = QLabel("🔍 号码次数查询")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title.setStyleSheet("color: white; margin: 0;")
        title_left_layout.addWidget(title)

        subtitle = QLabel("智能分析Excel数据中的号码分布情况")
        subtitle.setFont(QFont("Microsoft YaHei", 10))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.9); margin: 0;")
        title_left_layout.addWidget(subtitle)

        title_layout.addLayout(title_left_layout)
        title_layout.addStretch()

        # 右侧统计信息卡片 - 优化尺寸
        stats_card = QFrame()
        stats_card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
            }
        """)
        stats_card.setFixedSize(160, 45)

        stats_layout = QVBoxLayout(stats_card)
        stats_layout.setContentsMargins(8, 4, 8, 4)
        stats_layout.setSpacing(1)

        stats_title = QLabel("📈 统计概览")
        stats_title.setFont(QFont("Microsoft YaHei", 8, QFont.Weight.Bold))
        stats_title.setStyleSheet("color: white;")
        stats_layout.addWidget(stats_title)

        self.number_stats_label = QLabel("等待统计...")
        self.number_stats_label.setFont(QFont("Microsoft YaHei", 8))
        self.number_stats_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        stats_layout.addWidget(self.number_stats_label)

        title_layout.addWidget(stats_card)
        layout.addWidget(title_card)

        # 操作控制区域 - 优化布局
        control_container = QFrame()
        control_container.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.08);
            }
        """)

        control_layout = QVBoxLayout(control_container)
        control_layout.setContentsMargins(15, 12, 15, 12)
        control_layout.setSpacing(12)

        # 第一行：主要操作按钮 - 更紧凑的布局
        main_buttons_layout = QHBoxLayout()
        main_buttons_layout.setSpacing(12)

        # 统计按钮 - 主要操作，优化尺寸
        stats_btn = QPushButton("📊 开始统计")
        stats_btn.setFixedSize(120, 38)
        stats_btn.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        stats_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        stats_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 19px;
                font-weight: bold;
                padding: 0;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6fd8, stop:1 #6a4190);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4e5bc6, stop:1 #5e377e);
                transform: translateY(0px);
            }
        """)
        stats_btn.clicked.connect(self.analyze_all_numbers)
        main_buttons_layout.addWidget(stats_btn)

        # 刷新按钮 - 次要操作，优化尺寸
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.setFixedSize(100, 38)
        refresh_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Medium))
        refresh_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: white;
                color: #667eea;
                border: 2px solid #667eea;
                border-radius: 19px;
                font-weight: 500;
                padding: 0;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 0.1);
                border: 2px solid #5a6fd8;
                color: #5a6fd8;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: rgba(102, 126, 234, 0.2);
                transform: translateY(0px);
            }
        """)
        refresh_btn.clicked.connect(self.refresh_number_stats)
        main_buttons_layout.addWidget(refresh_btn)

        # 导出按钮 - 优化尺寸
        export_btn = QPushButton("📤 导出结果")
        export_btn.setFixedSize(100, 38)
        export_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Medium))
        export_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        export_btn.setStyleSheet("""
            QPushButton {
                background: white;
                color: #28a745;
                border: 2px solid #28a745;
                border-radius: 19px;
                font-weight: 500;
                padding: 0;
            }
            QPushButton:hover {
                background: rgba(40, 167, 69, 0.1);
                border: 2px solid #218838;
                color: #218838;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: rgba(40, 167, 69, 0.2);
                transform: translateY(0px);
            }
        """)
        export_btn.clicked.connect(self.export_number_stats)
        main_buttons_layout.addWidget(export_btn)

        main_buttons_layout.addStretch()

        # 返回主页按钮
        back_btn = QPushButton("🏠 返回主页")
        back_btn.setFixedSize(100, 38)
        back_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Medium))
        back_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        back_btn.setStyleSheet("""
            QPushButton {
                background: white;
                color: #6c757d;
                border: 2px solid #6c757d;
                border-radius: 19px;
                font-weight: 500;
                padding: 0;
            }
            QPushButton:hover {
                background: rgba(108, 117, 125, 0.1);
                border: 2px solid #5a6268;
                color: #5a6268;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: rgba(108, 117, 125, 0.2);
                transform: translateY(0px);
            }
        """)
        back_btn.clicked.connect(self.show_import_page)
        main_buttons_layout.addWidget(back_btn)

        control_layout.addLayout(main_buttons_layout)

        # 第二行：搜索和过滤 - 优化布局
        search_layout = QHBoxLayout()
        search_layout.setSpacing(8)

        # 搜索图标和标签 - 优化尺寸
        search_icon = QLabel("🔍")
        search_icon.setFont(QFont("Microsoft YaHei", 12))
        search_icon.setStyleSheet("color: #667eea;")
        search_layout.addWidget(search_icon)

        search_label = QLabel("快速搜索:")
        search_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Medium))
        search_label.setStyleSheet("color: #495057;")
        search_layout.addWidget(search_label)

        # 搜索输入框 - 优化尺寸和样式
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入号码进行实时搜索...")
        self.search_input.setFixedHeight(32)
        self.search_input.setMaximumWidth(300)
        self.search_input.setFont(QFont("Microsoft YaHei", 10))
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e9ecef;
                border-radius: 16px;
                padding: 6px 12px;
                background: #f8f9fa;
                color: #495057;
                selection-background-color: rgba(102, 126, 234, 0.3);
            }
            QLineEdit:focus {
                border: 2px solid #667eea;
                background: white;
                outline: none;
            }
            QLineEdit:hover {
                border: 2px solid #ced4da;
                background: white;
            }
        """)
        self.search_input.textChanged.connect(self.filter_number_table)
        search_layout.addWidget(self.search_input)

        search_layout.addStretch()

        control_layout.addLayout(search_layout)
        layout.addWidget(control_container)

        # 结果显示区域 - 优化设计
        result_container = QFrame()
        result_container.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.08);
            }
        """)

        result_layout = QVBoxLayout(result_container)
        result_layout.setContentsMargins(0, 0, 0, 0)
        result_layout.setSpacing(0)

        # 结果标题栏 - 优化高度
        title_bar = QFrame()
        title_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.1), stop:1 rgba(118, 75, 162, 0.1));
                border-radius: 12px 12px 0 0;
                border-bottom: 1px solid rgba(102, 126, 234, 0.2);
            }
        """)
        title_bar.setFixedHeight(50)

        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(15, 8, 15, 8)

        # 左侧标题和图标 - 优化布局
        title_left = QHBoxLayout()
        title_left.setSpacing(8)

        result_icon = QLabel("📊")
        result_icon.setFont(QFont("Microsoft YaHei", 14))
        result_icon.setStyleSheet("color: #667eea;")
        title_left.addWidget(result_icon)

        self.result_title = QLabel("号码统计结果")
        self.result_title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.result_title.setStyleSheet("color: #495057; margin: 0;")
        title_left.addWidget(self.result_title)

        title_bar_layout.addLayout(title_left)
        title_bar_layout.addStretch()

        # 右侧状态指示器 - 优化尺寸
        self.status_indicator = QLabel("●")
        self.status_indicator.setFont(QFont("Microsoft YaHei", 10))
        self.status_indicator.setStyleSheet("color: #6c757d;")
        title_bar_layout.addWidget(self.status_indicator)

        result_layout.addWidget(title_bar)

        # 内容区域 - 优化布局
        content_area = QFrame()
        content_area.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 0 0 12px 12px;
            }
        """)

        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(12)

        # 状态提示区域 - 优化样式和尺寸
        self.result_label = QLabel("点击'开始统计'按钮来分析所有号码数据")
        self.result_label.setFont(QFont("Microsoft YaHei", 11))
        self.result_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.15);
                border-radius: 10px;
                padding: 15px;
                min-height: 40px;
            }
        """)
        self.result_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(self.result_label)

        # 统计表格 - 优化样式和尺寸
        self.number_stats_table = QTableWidget()
        self.number_stats_table.setVisible(False)
        self.number_stats_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f1f3f4;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: rgba(102, 126, 234, 0.15);
                border: 1px solid #e9ecef;
                border-radius: 8px;
                font-size: 10px;
            }
            QTableWidget::item {
                padding: 10px 6px;
                border-bottom: 1px solid #f1f3f4;
                color: #495057;
            }
            QTableWidget::item:selected {
                background-color: rgba(102, 126, 234, 0.15);
                color: #495057;
            }
            QTableWidget::item:hover {
                background-color: rgba(102, 126, 234, 0.08);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 10px 6px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 10px;
            }
            QHeaderView::section:first {
                border-top-left-radius: 8px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 8px;
                border-right: none;
            }
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #ced4da;
                border-radius: 5px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
        """)
        self.number_stats_table.setAlternatingRowColors(True)
        self.number_stats_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.number_stats_table.setSortingEnabled(True)
        self.number_stats_table.verticalHeader().setVisible(False)
        self.number_stats_table.horizontalHeader().setStretchLastSection(True)
        content_layout.addWidget(self.number_stats_table)

        result_layout.addWidget(content_area)
        layout.addWidget(result_container, 1)

        return page

    def refresh_transit_data(self):
        """刷新在途数据 - 兼容旧方法名"""
        self.refresh_transit_preview_data()



    def show_transit_context_menu(self, position):
        """显示在途数据表格的右键菜单"""
        if self.transit_table.itemAt(position) is None:
            return

        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 3px;
            }
            QMenu::item:selected {
                background-color: #667eea;
                color: white;
            }
        """)

        # 查看详细操作
        detail_action = menu.addAction("🔍 查看详细操作")
        detail_action.triggered.connect(lambda: self.show_transit_detail_page(self.transit_table.itemAt(position))) # type: ignore

        menu.addSeparator()

        # 标记为已归档
        archive_action = menu.addAction("📋 标记为已归档")
        archive_action.triggered.connect(self.mark_single_archived) # type: ignore

        # 标记为在途
        transit_action = menu.addAction("🚛 标记为在途")
        transit_action.triggered.connect(self.mark_single_transit) # type: ignore

        menu.addSeparator()

        # 回退工单（清除处理结果）
        rollback_action = menu.addAction("↩️ 回退工单")
        rollback_action.triggered.connect(self.rollback_work_order) # type: ignore

        menu.exec(self.transit_table.mapToGlobal(position))

    def mark_single_archived(self):
        """标记单行为已归档"""
        self.mark_single_status("已归档")

    def mark_single_transit(self):
        """标记单行为在途"""
        self.mark_single_status("在途")

    def mark_single_status(self, status):
        """标记单行的归档状态"""
        current_row = self.transit_table.currentRow()
        if current_row < 0:
            return

        try:
            # 直接使用Q列（索引为16）作为归档状态列
            archive_column = 16  # Q列的索引

            # 获取原始行号
            original_row_item = self.transit_table.item(current_row, 0)  # 行号列
            if original_row_item:
                original_row_num = int(original_row_item.text())
                # 在原始数据中更新
                data_row_idx = original_row_num - 1  # 转换为数组索引
                if self.current_data and 0 <= data_row_idx < len(self.current_data):
                    # 确保行有足够的列
                    while len(self.current_data[data_row_idx]) <= archive_column:
                        self.current_data[data_row_idx].append("")
                    # 更新状态
                    self.current_data[data_row_idx][archive_column] = status

                    # 刷新表格显示
                    self.refresh_transit_data()

                    QMessageBox.information(self, "操作完成", f"已将第{original_row_num}行标记为'{status}'")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"标记操作失败: {str(e)}")

    def rollback_work_order(self):
        """回退工单 - 清除处理结果并恢复为在途状态"""
        current_row = self.transit_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要回退的工单")
            return

        try:
            # 获取原始行号
            original_row_item = self.transit_table.item(current_row, 0)  # 行号列
            if not original_row_item:
                QMessageBox.warning(self, "错误", "无法获取工单行号")
                return

            original_row_num = int(original_row_item.text())

            # 确认回退操作
            reply = QMessageBox.question(
                self,
                "确认回退",
                f"确定要回退第{original_row_num}行的工单吗？\n\n此操作将：\n"
                "• 清除所有流程处理结果（是/否选择）\n"
                "• 将工单状态恢复为在途\n"
                "• 删除流程进度记录\n\n"
                "此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 执行回退操作
            success = self.perform_work_order_rollback(original_row_num)

            if success:
                # 刷新显示
                self.refresh_transit_data()
                self.update_transit_stats()
                QMessageBox.information(self, "回退成功", f"第{original_row_num}行工单已成功回退到在途状态")
            else:
                QMessageBox.warning(self, "回退失败", "工单回退操作失败，请检查日志")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"回退工单时出错: {str(e)}")

    def perform_work_order_rollback(self, excel_row_number):
        """执行工单回退操作"""
        try:
            print(f"开始回退第{excel_row_number}行工单...")

            # 1. 获取工单ID
            work_order_id = self.get_work_order_id_by_row(excel_row_number)
            if not work_order_id:
                print(f"未找到第{excel_row_number}行对应的工单ID")
                return False

            print(f"找到工单ID: {work_order_id}")

            # 2. 清除数据库中的流程处理结果（R-AD列）
            success = self.clear_work_order_process_results(work_order_id)
            if not success:
                print("清除数据库流程结果失败")
                return False

            # 3. 删除流程进度记录
            success = self.delete_process_progress(work_order_id)
            if not success:
                print("删除流程进度记录失败")
                return False

            # 4. 更新内存中的数据状态为在途
            self.update_memory_data_status(excel_row_number, "在途")

            print(f"第{excel_row_number}行工单回退完成")
            return True

        except Exception as e:
            print(f"执行工单回退时出错: {e}")
            return False

    def clear_work_order_process_results(self, work_order_id):
        """清除工单的流程处理结果（R-AD列）"""
        try:
            # 定义需要清空的列（R-AD列对应的数据库字段）
            columns_to_clear = [
                'planning_big_network', 'planning_non_big_network',  # R-S列
                'construction_property', 'construction_transmission', 'construction_power',
                'construction_tower', 'construction_equipment',  # T-X列
                'maintenance_sporadic', 'maintenance_outsourced',  # Y-Z列
                'optimization_antenna', 'optimization_backend',  # AA-AB列
                'customer_field_test', 'customer_communication'  # AC-AD列
            ]

            # 构建更新SQL
            set_clauses = [f"{col} = NULL" for col in columns_to_clear]
            set_clauses.append("updated_at = CURRENT_TIMESTAMP")

            sql = f"UPDATE work_orders SET {', '.join(set_clauses)} WHERE id = ?"

            # 执行更新
            conn = self.data_manager.db.get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, (work_order_id,))
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            print(f"成功清除工单 {work_order_id} 的流程处理结果，影响行数: {affected_rows}")
            return affected_rows > 0

        except Exception as e:
            print(f"清除工单流程结果时出错: {e}")
            return False

    def delete_process_progress(self, work_order_id):
        """删除工单的流程进度记录"""
        try:
            conn = self.data_manager.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('DELETE FROM process_progress WHERE work_order_id = ?', (work_order_id,))
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            print(f"成功删除工单 {work_order_id} 的流程进度记录，影响行数: {affected_rows}")
            return True

        except Exception as e:
            print(f"删除流程进度记录时出错: {e}")
            return False

    def update_memory_data_status(self, excel_row_number, status):
        """更新内存中数据的归档状态"""
        try:
            # Q列索引为16
            archive_column = 16

            # 转换为数组索引
            data_row_idx = excel_row_number - 1

            if self.current_data and 0 <= data_row_idx < len(self.current_data):
                # 确保行有足够的列
                while len(self.current_data[data_row_idx]) <= archive_column:
                    self.current_data[data_row_idx].append("")

                # 更新状态
                self.current_data[data_row_idx][archive_column] = status

                # 同时清除内存中的R-AD列数据（索引17-29）
                for col_idx in range(17, 30):  # R列(17)到AD列(29)
                    if len(self.current_data[data_row_idx]) > col_idx:
                        self.current_data[data_row_idx][col_idx] = ""

                print(f"已更新第{excel_row_number}行的内存数据状态为: {status}")
                return True
            else:
                print(f"无效的行号: {excel_row_number}")
                return False

        except Exception as e:
            print(f"更新内存数据状态时出错: {e}")
            return False

    def show_row_detail(self):
        """显示行的详细信息"""
        current_row = self.transit_table.currentRow()
        if current_row < 0:
            return

        try:
            # 获取原始行号
            original_row_item = self.transit_table.item(current_row, 0)
            if original_row_item:
                original_row_num = int(original_row_item.text())
                data_row_idx = original_row_num - 1

                if self.current_data and 0 <= data_row_idx < len(self.current_data):
                    row_data = self.current_data[data_row_idx]

                    # 构建详细信息
                    detail_text = f"第 {original_row_num} 行详细信息:\n\n"

                    # 显示主要字段
                    field_names = ["A列", "B列", "C列", "D列", "E列", "F列", "G列", "H列", "I列",
                                 "J列", "K列", "L列", "M列", "N列", "O列", "P列", "Q列"]

                    for i, field_name in enumerate(field_names):
                        if i < len(row_data):
                            value = str(row_data[i]) if row_data[i] is not None else "空"
                            detail_text += f"{field_name}: {value}\n"

                    # 显示归档状态（Q列）
                    archive_column = 16  # Q列的索引
                    if archive_column < len(row_data):
                        archive_status = str(row_data[archive_column]) if row_data[archive_column] else "空"
                        detail_text += f"\nQ列(归档状态): {archive_status}"

                    # 显示详细信息对话框
                    msg = QMessageBox(self)
                    msg.setWindowTitle("行详细信息")
                    msg.setText(detail_text)
                    msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                    msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示详细信息失败: {str(e)}")

    def create_archive_analysis_buttons(self, parent_layout):
        """创建归档分析功能按钮区域"""
        analysis_group = QGroupBox("数据分析可视化")
        analysis_group.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        analysis_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: rgba(102, 126, 234, 0.05);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #667eea;
            }
        """)

        analysis_layout = QGridLayout(analysis_group)
        analysis_layout.setSpacing(15)
        analysis_layout.setContentsMargins(20, 25, 20, 20)

        # 创建四个分析按钮和描述
        buttons_data = [
            ("🌍 区域分析", "分析不同区域的数据分布和特征", "区域分析"),
            ("📅 月份变化", "查看数据随月份的变化趋势", "月份变化"),
            ("⏱️ 归档时长", "统计和分析数据归档时长", "归档时长"),
            ("🔍 原因分析", "深入分析数据背后的原因", "原因分析")
        ]

        self.archive_analysis_buttons = []

        for i, (title, description, analysis_type) in enumerate(buttons_data):
            # 创建按钮容器
            button_container = QWidget()
            container_layout = QVBoxLayout(button_container)
            container_layout.setSpacing(8)
            container_layout.setContentsMargins(0, 0, 0, 0)

            # 创建按钮
            button = self.create_analysis_button(title, description)
            button.clicked.connect(lambda checked, at=analysis_type: self.perform_analysis(at))
            self.archive_analysis_buttons.append(button)

            # 创建描述标签
            desc_label = QLabel(description)
            desc_label.setFont(QFont("Microsoft YaHei", 9))
            desc_label.setStyleSheet("color: #666; text-align: center;")
            desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            desc_label.setWordWrap(True)

            container_layout.addWidget(button)
            container_layout.addWidget(desc_label)

            # 添加到网格布局
            row = i // 2
            col = i % 2
            analysis_layout.addWidget(button_container, row, col)

        # 初始状态根据数据情况启用按钮
        self.set_archive_analysis_buttons_enabled(self.current_data is not None)

        parent_layout.addWidget(analysis_group)

    def create_test_time_edit_page(self):
        """创建修改现场测试时间页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("⏰ 修改现场测试时间")
        title_label.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #333; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("此功能用于修改工单的现场测试时间（J列数据），修改后将保存到数据库")
        info_label.setFont(QFont("Microsoft YaHei", 12))
        info_label.setStyleSheet("color: #666; margin-bottom: 20px;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)

        # 搜索和筛选区域
        search_group = QGroupBox("搜索工单")
        search_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        search_layout = QHBoxLayout(search_group)
        search_layout.setSpacing(15)

        # 搜索输入框
        self.test_time_search_input = QLineEdit()
        self.test_time_search_input.setPlaceholderText("输入工单号、地址或其他信息进行搜索...")
        self.test_time_search_input.setFont(QFont("Microsoft YaHei", 11))
        self.test_time_search_input.setFixedHeight(35)
        self.test_time_search_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 5px 10px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        self.test_time_search_input.textChanged.connect(self.filter_test_time_data)
        search_layout.addWidget(self.test_time_search_input)

        # 搜索按钮
        search_btn = ModernButton("🔍 搜索", True)
        search_btn.setFixedWidth(80)
        search_btn.setFixedHeight(35)
        search_btn.clicked.connect(self.filter_test_time_data)
        search_layout.addWidget(search_btn)

        # 刷新按钮
        refresh_btn = ModernButton("🔄 刷新", True)
        refresh_btn.setFixedWidth(80)
        refresh_btn.setFixedHeight(35)
        refresh_btn.clicked.connect(self.refresh_test_time_data)
        search_layout.addWidget(refresh_btn)

        layout.addWidget(search_group)

        # 数据表格
        table_group = QGroupBox("工单列表")
        table_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        table_layout = QVBoxLayout(table_group)

        # 创建表格
        self.test_time_table = QTableWidget()
        self.test_time_table.setFont(QFont("Microsoft YaHei", 10))
        self.test_time_table.setAlternatingRowColors(True)
        self.test_time_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.test_time_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)

        # 设置表格列
        self.test_time_table.setColumnCount(6)
        self.test_time_table.setHorizontalHeaderLabels([
            "行号", "工单号", "地址", "当前测试时间", "操作", "最后修改"
        ])

        table_layout.addWidget(self.test_time_table)
        layout.addWidget(table_group)

        # 返回按钮
        back_layout = QHBoxLayout()
        back_layout.addStretch()

        back_btn = ModernButton("← 返回主页")
        back_btn.setFixedWidth(120)
        back_btn.setFixedHeight(40)
        back_btn.clicked.connect(self.show_import_page)
        back_layout.addWidget(back_btn)

        layout.addLayout(back_layout)

        return page

    def create_data_management_page(self):
        """创建数据管理页面"""
        # 创建主容器
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(0, 0, 0, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: rgba(102, 126, 234, 0.5);
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(102, 126, 234, 0.7);
            }
        """)

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)  # 减小边距
        layout.setSpacing(15)  # 减小间距

        # 将内容容器设置到滚动区域
        scroll_area.setWidget(content_widget)

        # 创建页面主布局
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.addWidget(scroll_area)

        # 页面标题 - 更紧凑
        title = QLabel("🗄️ 数据管理")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))  # 减小字体
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFixedHeight(40)  # 固定高度
        layout.addWidget(title)

        # 数据统计信息 - 更紧凑
        stats_frame = QFrame()
        stats_frame.setFixedHeight(60)  # 固定高度
        stats_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.05), stop:1 rgba(118, 75, 162, 0.05));
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 10px;
                padding: 8px;
            }
        """)

        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(10, 5, 10, 5)

        self.stats_info_label = QLabel("正在加载统计信息...")
        self.stats_info_label.setFont(QFont("Microsoft YaHei", 10))  # 减小字体
        self.stats_info_label.setStyleSheet("color: #667eea;")
        self.stats_info_label.setWordWrap(True)  # 允许换行
        stats_layout.addWidget(self.stats_info_label, 1)  # 给予更多空间

        # 按钮容器
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        refresh_btn = ModernButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_data_stats)
        refresh_btn.setFixedSize(80, 40)  # 减小按钮尺寸
        button_layout.addWidget(refresh_btn)

        # 导出全部数据按钮
        export_all_btn = ModernButton("📤 导出全部数据", True)
        export_all_btn.clicked.connect(self.export_all_data)
        export_all_btn.setFixedSize(110, 40)  # 减小按钮尺寸
        button_layout.addWidget(export_all_btn)

        stats_layout.addLayout(button_layout)
        layout.addWidget(stats_frame)

        # 上传记录表格 - 更紧凑
        records_group = QGroupBox("上传记录")
        records_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))  # 减小字体
        records_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 10px;
                margin-top: 8px;
                padding-top: 12px;
                background: rgba(102, 126, 234, 0.05);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #667eea;
            }
        """)

        records_layout = QVBoxLayout(records_group)
        records_layout.setContentsMargins(15, 20, 15, 15)  # 减小边距

        # 创建表格
        self.records_table = QTableWidget()
        self.records_table.setColumnCount(8)  # 增加一列用于导出按钮
        self.records_table.setHorizontalHeaderLabels([
            "ID", "文件名", "上传时间", "总行数", "有效行数", "状态", "删除", "导出"
        ])

        # 设置表格样式 - 更紧凑
        self.records_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(102, 126, 234, 0.2);
                background-color: white;
                alternate-background-color: rgba(102, 126, 234, 0.05);
                selection-background-color: rgba(102, 126, 234, 0.3);
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid rgba(102, 126, 234, 0.1);
            }
            QHeaderView::section {
                background-color: rgba(102, 126, 234, 0.1);
                padding: 6px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
        """)

        self.records_table.setAlternatingRowColors(True)
        self.records_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 设置表格列宽自适应
        header = self.records_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, header.ResizeMode.ResizeToContents)  # ID列
        header.setSectionResizeMode(1, header.ResizeMode.Stretch)  # 文件名列
        header.setSectionResizeMode(2, header.ResizeMode.ResizeToContents)  # 上传时间列
        header.setSectionResizeMode(3, header.ResizeMode.ResizeToContents)  # 总行数列
        header.setSectionResizeMode(4, header.ResizeMode.ResizeToContents)  # 有效行数列
        header.setSectionResizeMode(5, header.ResizeMode.ResizeToContents)  # 状态列
        header.setSectionResizeMode(6, header.ResizeMode.ResizeToContents)  # 删除列
        header.setSectionResizeMode(7, header.ResizeMode.ResizeToContents)  # 导出列

        # 设置表格最小高度，但允许滚动
        self.records_table.setMinimumHeight(200)
        self.records_table.setMaximumHeight(400)

        records_layout.addWidget(self.records_table)
        layout.addWidget(records_group, 1)

        return page

    def create_analysis_buttons(self):
        """创建分析功能按钮区域"""
        analysis_group = QGroupBox("数据分析可视化")
        analysis_group.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        analysis_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background: rgba(102, 126, 234, 0.05);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #667eea;
            }
        """)

        analysis_layout = QGridLayout(analysis_group)
        analysis_layout.setSpacing(15)
        analysis_layout.setContentsMargins(20, 25, 20, 20)

        # 创建四个分析按钮和描述
        buttons_data = [
            ("🌍 区域分析", "分析不同区域的数据分布和特征", "区域分析"),
            ("📅 月份变化", "查看数据随月份的变化趋势", "月份变化"),
            ("⏱️ 归档时长", "统计和分析数据归档时长", "归档时长"),
            ("🔍 原因分析", "深入分析数据背后的原因", "原因分析")
        ]

        self.analysis_buttons = []

        for i, (title, description, analysis_type) in enumerate(buttons_data):
            # 创建按钮容器
            button_container = QWidget()
            container_layout = QVBoxLayout(button_container)
            container_layout.setSpacing(8)
            container_layout.setContentsMargins(0, 0, 0, 0)

            # 创建按钮
            button = self.create_analysis_button(title, description)
            button.clicked.connect(lambda checked, at=analysis_type: self.perform_analysis(at))
            self.analysis_buttons.append(button)

            # 创建描述标签
            desc_label = QLabel(description)
            desc_label.setFont(QFont("Microsoft YaHei", 9))
            desc_label.setStyleSheet("color: #666; text-align: center;")
            desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            desc_label.setWordWrap(True)

            container_layout.addWidget(button)
            container_layout.addWidget(desc_label)

            # 添加到网格布局
            row = i // 2
            col = i % 2
            analysis_layout.addWidget(button_container, row, col)

        # 初始状态禁用按钮
        self.set_analysis_buttons_enabled(False)

        self.content_layout.addWidget(analysis_group)

    def create_analysis_button(self, title, description):
        """创建单个分析按钮 - 类似ModernButton的效果"""
        button = QPushButton(title)
        button.setFixedHeight(70)
        button.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        button.setCursor(Qt.CursorShape.PointingHandCursor)

        # 设置工具提示
        button.setToolTip(description)

        button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 35px;
                font-weight: bold;
                padding: 0 25px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6fd8, stop:1 #6a4190);
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4e5bc6, stop:1 #5e377e);
                transform: translateY(-1px);
            }
            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #cccccc, stop:1 #999999);
                color: #666666;
                transform: none;
                box-shadow: none;
            }
        """)

        return button

    def set_analysis_buttons_enabled(self, enabled):
        """设置分析按钮的启用状态"""
        if hasattr(self, 'analysis_buttons'):
            for button in self.analysis_buttons:
                button.setEnabled(enabled)

    def set_archive_analysis_buttons_enabled(self, enabled):
        """设置归档分析按钮的启用状态"""
        if hasattr(self, 'archive_analysis_buttons'):
            for button in self.archive_analysis_buttons:
                button.setEnabled(enabled)

    def show_main_page(self):
        """显示主页面"""
        self.stacked_widget.setCurrentWidget(self.main_page)

    def show_archive_visualization_page(self):
        """显示归档可视化页面"""
        # 更新按钮状态
        self.update_sidebar_buttons(self.archive_viz_btn)

        # 切换到归档可视化页面
        self.stacked_widget.setCurrentWidget(self.archive_visualization_page)

        # 更新按钮状态
        if hasattr(self, 'archive_analysis_buttons'):
            self.set_archive_analysis_buttons_enabled(self.current_data is not None)

    def show_transit_data_page(self):
        """显示在途数据处理页面"""
        # 更新按钮状态
        self.update_sidebar_buttons(self.transit_data_btn)

        # 切换到在途数据处理页面
        self.stacked_widget.setCurrentWidget(self.transit_data_page)

        # 自动刷新在途数据
        if self.current_data:
            self.update_transit_stats()
            self.refresh_transit_preview_data()
        else:
            self.transit_stats_label.setText("请先导入Excel文件")
            if hasattr(self, 'process_stats_label'):
                self.process_stats_label.setText("暂无数据")

    def show_number_query_page(self):
        """显示号码统计页面"""
        # 更新按钮状态
        self.update_sidebar_buttons(self.number_query_btn)

        # 立即切换到号码统计页面，避免卡顿
        self.stacked_widget.setCurrentWidget(self.number_query_page)

        # 清空之前的统计结果
        self.result_label.setText("点击'开始统计'按钮来分析所有号码数据")
        self.result_label.setStyleSheet("""
            color: #666;
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            padding: 15px;
            min-height: 50px;
        """)

        # 隐藏统计表格
        if hasattr(self, 'number_stats_table'):
            self.number_stats_table.setVisible(False)

        # 清空搜索框
        if hasattr(self, 'search_input'):
            self.search_input.clear()

        # 更新统计信息
        if hasattr(self, 'number_stats_label'):
            self.number_stats_label.setText("")



    def refresh_transit_preview_data(self):
        """刷新在途数据预览"""
        if not self.current_data:
            self.transit_table.setRowCount(0)
            self.transit_table.setColumnCount(0)
            return

        try:
            # 直接使用Q列（索引为16）作为归档状态列
            archive_column = 16  # Q列的索引

            # 筛选在途数据（为空或为"在途"）
            transit_data = []
            for row_idx, row in enumerate(self.current_data[2:], start=3):  # 从第3行开始
                if len(row) > archive_column:
                    archive_status = str(row[archive_column]).strip() if row[archive_column] else ""
                    # 检查是否为在途状态（空值、在途、未归档等）
                    if not archive_status or archive_status.lower() in ['在途', '未归档', '0', '否', 'no', 'false', '']:
                        # 添加行号信息
                        row_with_index = [row_idx] + list(row)
                        transit_data.append(row_with_index)

            # 更新预览表格显示
            self.update_transit_preview_table(transit_data)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新在途数据预览时出错: {str(e)}")

    def update_transit_preview_table(self, transit_data):
        """更新在途数据预览表格"""
        if not transit_data:
            self.transit_table.setRowCount(0)
            self.transit_table.setColumnCount(0)
            return

        # 设置表格列数和表头
        display_columns = [0, 1, 2, 3, 9, 16, 17]  # 行号、A、B、C、I、P、Q
        display_headers = ["行号", "A列", "B列", "C列", "I列(号码)", "P列(原因)", "Q列(状态)", "流程进度"]

        self.transit_table.setColumnCount(len(display_headers))
        self.transit_table.setHorizontalHeaderLabels(display_headers)
        self.transit_table.setRowCount(len(transit_data))

        # 填充数据
        for row_idx, row_data in enumerate(transit_data):
            # 填充原有列数据
            for col_idx, col_pos in enumerate(display_columns):
                if col_pos < len(row_data):
                    item_text = str(row_data[col_pos]) if row_data[col_pos] is not None else ""
                    item = QTableWidgetItem(item_text)

                    # 设置状态列的颜色
                    if col_idx == 6:  # Q列(状态)
                        if not item_text or item_text.lower() in ['在途', '未归档', '0', '否', 'no', 'false', '']:
                            from PyQt6.QtGui import QColor
                            item.setBackground(QColor("#fff3cd"))
                            item.setForeground(QColor("#856404"))

                    self.transit_table.setItem(row_idx, col_idx, item)

            # 添加流程进度列
            excel_row_number = row_data[0]  # 获取Excel行号
            progress_text = self.get_process_progress_text(excel_row_number)
            progress_item = QTableWidgetItem(progress_text)

            # 根据流程进度设置颜色
            from PyQt6.QtGui import QColor
            if progress_text == "未开始":
                progress_item.setBackground(QColor("#f8f9fa"))
                progress_item.setForeground(QColor("#6c757d"))
            elif "进行中" in progress_text:
                progress_item.setBackground(QColor("#d1ecf1"))
                progress_item.setForeground(QColor("#0c5460"))
            elif progress_text == "已完成":
                progress_item.setBackground(QColor("#d4edda"))
                progress_item.setForeground(QColor("#155724"))
            else:
                progress_item.setBackground(QColor("#fff3cd"))
                progress_item.setForeground(QColor("#856404"))

            self.transit_table.setItem(row_idx, len(display_columns), progress_item)

        # 调整列宽
        self.transit_table.resizeColumnsToContents()

    def get_process_progress_text(self, excel_row_number):
        """获取流程进度文本"""
        try:
            # 根据Excel行号获取工单ID
            work_order_id = self.get_work_order_id_by_row(excel_row_number)
            if not work_order_id:
                return "未开始"

            # 获取流程进度
            progress = self.data_manager.get_process_progress(work_order_id)
            if not progress:
                return "未开始"

            # 根据流程状态返回相应文本
            if progress['status'] == 'completed':
                return "已完成"
            elif progress['status'] == 'in_progress':
                selected_processes = progress['selected_processes']
                current_index = progress['current_process_index']

                if current_index < len(selected_processes):
                    current_process = selected_processes[current_index]
                    # 流程名称映射
                    process_names = {
                        'planning': '规划',
                        'construction': '建设',
                        'maintenance': '维护',
                        'optimization': '优化',
                        'customer': '客户'
                    }
                    current_name = process_names.get(current_process, current_process)
                    return f"{current_name}进行中 ({current_index + 1}/{len(selected_processes)})"
                else:
                    return "流程异常"
            else:
                return "未开始"

        except Exception as e:
            print(f"获取流程进度失败: {e}")
            return "未开始"

    def show_transit_detail_page(self, item):
        """显示在途数据详细操作页面"""
        if not item:
            return

        # 获取选中行的数据
        row = item.row()
        row_data = []
        for col in range(self.transit_table.columnCount()):
            cell_item = self.transit_table.item(row, col)
            row_data.append(cell_item.text() if cell_item else "")

        # 获取新的工单ID - 强化版本，确保在打包环境中也能正常工作
        try:
            excel_row_number = int(row_data[0])  # 表格第一列是Excel行号
            new_work_order_id = self.get_work_order_id_by_row(excel_row_number)
            print(f"🔍 准备切换到工单ID: {new_work_order_id} (Excel行号: {excel_row_number})")

            # 强化的工单ID比较逻辑
            current_id = getattr(self, 'current_work_order_id', None)

            # 检查是否是同一个工单 - 使用更严格的比较
            if (current_id is not None and
                new_work_order_id is not None and
                str(current_id) == str(new_work_order_id)):
                print(f"✅ 仍然是同一个工单 (ID: {current_id})，保持当前状态")
            else:
                print(f"🔄 切换到新工单 (从 {current_id} 到 {new_work_order_id})，强制清理之前的状态")

                # 强制清理之前工单的状态 - 多重保险
                try:
                    self.clear_previous_work_order_state()
                    # 额外清理页面选择状态缓存，防止状态污染
                    self.current_page_selections = {}
                    # 强制清理所有流程页面的UI状态，确保不会显示之前工单的选择
                    self.clear_all_process_page_selections()
                    print("🧹 已清理页面选择状态缓存和UI状态")
                except Exception as clear_error:
                    print(f"⚠️ 清理状态时出错，执行紧急清理: {clear_error}")
                    # 紧急清理
                    try:
                        if hasattr(self, 'process_checkboxes'):
                            for cb in self.process_checkboxes.values():
                                if cb: cb.setChecked(False)
                        if hasattr(self, 'process_reason_combos'):
                            for combo in self.process_reason_combos.values():
                                if combo: combo.setCurrentIndex(0)
                        # 紧急清理页面选择状态
                        self.current_page_selections = {}
                    except:
                        pass

                # 设置新的工单ID
                self.current_work_order_id = new_work_order_id

                # 保存当前工单数据
                self.current_work_order_data = row_data

                # 尝试恢复新工单的流程进度
                try:
                    self.restore_process_progress()
                except Exception as restore_error:
                    print(f"⚠️ 恢复流程进度时出错: {restore_error}")

        except (ValueError, IndexError) as e:
            print(f"获取工单ID失败: {e}")
            # 清理状态并设置为None - 强制清理
            try:
                self.clear_previous_work_order_state()
            except:
                # 紧急清理
                try:
                    if hasattr(self, 'process_checkboxes'):
                        for cb in self.process_checkboxes.values():
                            if cb: cb.setChecked(False)
                    if hasattr(self, 'process_reason_combos'):
                        for combo in self.process_reason_combos.values():
                            if combo: combo.setCurrentIndex(0)
                except:
                    pass

            self.current_work_order_id = None
            self.current_work_order_data = row_data

        # 切换到详细操作页面
        self.stacked_widget.setCurrentWidget(self.transit_detail_page)

        # 更新详细信息显示
        self.update_transit_detail_info(row_data)

    def clear_previous_work_order_state(self):
        """清理之前工单的状态 - 强化版本，确保在打包环境中也能正常工作"""
        try:
            print("🧹 清理之前工单的状态...")

            # 强制清理流程选择状态 - 使用更安全的方式
            try:
                if hasattr(self, 'process_checkboxes') and self.process_checkboxes:
                    for key, checkbox in self.process_checkboxes.items():
                        if checkbox and hasattr(checkbox, 'setChecked'):
                            checkbox.setChecked(False)
                            print(f"  清理复选框: {key}")
            except Exception as e:
                print(f"  清理复选框时出错: {e}")

            # 强制清理流程原因选择 - 使用更安全的方式
            try:
                if hasattr(self, 'process_reason_combos') and self.process_reason_combos:
                    for key, combo in self.process_reason_combos.items():
                        if combo and hasattr(combo, 'setCurrentIndex'):
                            combo.setCurrentIndex(0)  # 重置为"请选择原因"
                            print(f"  清理下拉框: {key}")
            except Exception as e:
                print(f"  清理下拉框时出错: {e}")

            # 强制清理流程进度相关的内存状态 - 使用更安全的删除方式
            state_attrs = [
                'selected_processes',
                'selected_process_details',
                'current_process_index',
                'process_results',
                'current_page_selections',
                'pending_page_selections'
            ]

            for attr_name in state_attrs:
                try:
                    if hasattr(self, attr_name):
                        delattr(self, attr_name)
                        print(f"  清理属性: {attr_name}")
                except Exception as e:
                    print(f"  清理属性 {attr_name} 时出错: {e}")

            # 强制清理所有流程页面的环节状态选择
            try:
                self.clear_all_process_page_selections()
            except Exception as e:
                print(f"  清理流程页面选择时出错: {e}")

            # 额外的强制清理 - 直接重置可能存在的状态变量
            try:
                self.selected_processes = []
                self.selected_process_details = []
                self.current_process_index = 0
                self.process_results = {}
                self.current_page_selections = {}
                self.pending_page_selections = {}
                print("  强制重置状态变量")
            except Exception as e:
                print(f"  强制重置状态变量时出错: {e}")

            print("✅ 之前工单状态已清理完成")

        except Exception as e:
            print(f"❌ 清理之前工单状态时出错: {e}")
            # 即使出错也要尝试基本清理
            try:
                self.selected_processes = []
                self.selected_process_details = []
                self.current_process_index = 0
                self.process_results = {}
                print("  执行了紧急清理")
            except:
                pass  # 最后的保险

    def clear_all_process_page_selections(self):
        """清理所有流程页面的环节状态选择"""
        try:
            # 定义所有流程页面的选择配置
            process_configs = {
                'planning': {
                    'items': ['大网', '非大网']
                },
                'construction': {
                    'items': ['设计', '勘察', '施工', '监理', '验收']
                },
                'maintenance': {
                    'items': ['故障处理', '预防维护', '设备检修', '性能优化']
                },
                'optimization': {
                    'items': ['网络优化', '参数调整', '容量扩展', '覆盖优化']
                },
                'customer': {
                    'items': ['投诉处理', '服务改进', '满意度调查', '回访确认']
                }
            }

            # 清理每个流程页面的选择状态
            for process_key, config in process_configs.items():
                if hasattr(self, 'process_pages') and process_key in self.process_pages:
                    page = self.process_pages[process_key]

                    # 查找页面中的所有按钮组并清理选择状态
                    for child in page.findChildren(QButtonGroup):
                        if child.checkedButton():
                            child.setExclusive(False)  # 临时取消互斥
                            child.checkedButton().setChecked(False)  # 取消选中
                            child.setExclusive(True)  # 恢复互斥

            print("✅ 所有流程页面的环节状态已清理")

        except Exception as e:
            print(f"❌ 清理流程页面选择状态时出错: {e}")

    def clear_current_page_selections(self, process_key):
        """清理指定流程页面的环节状态选择"""
        try:
            if hasattr(self, 'process_pages') and process_key in self.process_pages:
                page = self.process_pages[process_key]

                # 查找页面中的所有按钮组并清理选择状态
                for child in page.findChildren(QButtonGroup):
                    if child.checkedButton():
                        child.setExclusive(False)  # 临时取消互斥
                        child.checkedButton().setChecked(False)  # 取消选中
                        child.setExclusive(True)  # 恢复互斥

                print(f"✅ {process_key} 流程页面的环节状态已清理")

        except Exception as e:
            print(f"❌ 清理 {process_key} 流程页面选择状态时出错: {e}")

    def update_transit_detail_info(self, row_data):
        """更新在途数据详细信息显示"""
        if not row_data:
            return

        # 更新标题
        self.detail_title_label.setText(f"在途数据详细信息 - 行号: {row_data[0]}")

        # 更新详细信息
        info_text = f"工单编号: {row_data[1]} | 类型: {row_data[2]} | 描述: {row_data[3]}\n"
        info_text += f"号码: {row_data[4]} | 原因: {row_data[5]} | 状态: {row_data[6] if row_data[6] else '在途'}"

        self.detail_info_label.setText(info_text)

    def get_work_order_id_by_row(self, excel_row_number):
        """根据Excel行号获取工单ID"""
        try:
            if hasattr(self, 'data_manager') and self.data_manager:
                # 从数据库查询对应行号的工单ID
                conn = self.data_manager.db.get_connection()
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id FROM work_orders
                    WHERE row_number = ?
                    ORDER BY created_at DESC LIMIT 1
                ''', (excel_row_number,))

                result = cursor.fetchone()
                conn.close()

                if result:
                    return result['id']

            return None

        except Exception as e:
            print(f"根据行号获取工单ID失败: {e}")
            return None

    def restore_process_progress(self):
        """恢复流程进度"""
        try:
            if not hasattr(self, 'current_work_order_id') or not self.current_work_order_id:
                return

            # 从数据库获取流程进度
            progress = self.data_manager.get_process_progress(self.current_work_order_id)

            if progress:
                # 恢复流程状态
                self.selected_processes = progress['selected_processes']
                self.selected_process_details = progress['selected_process_details']
                self.current_process_index = progress['current_process_index']

                if not hasattr(self, 'process_results'):
                    self.process_results = {}
                self.process_results.update(progress['process_results'])

                # 恢复当前页面选择状态 - 但要确保是当前工单的状态
                # 只有当进度记录确实属于当前工单时才恢复选择状态
                if progress.get('work_order_id') == self.current_work_order_id:
                    self.current_page_selections = progress.get('current_page_selections', {})
                    print(f"✅ 恢复工单 {self.current_work_order_id} 的页面选择状态")
                else:
                    self.current_page_selections = {}
                    print(f"⚠️ 进度记录工单ID不匹配，清空页面选择状态")

                # 更新界面显示
                self.update_process_selection_ui()

                # 显示恢复信息
                from PyQt6.QtWidgets import QMessageBox
                current_process = self.selected_processes[self.current_process_index] if self.current_process_index < len(self.selected_processes) else "已完成"

                # 创建自定义消息框，包含三个选项
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("发现未完成的流程")
                msg_box.setText(f"检测到该工单有未完成的流程处理：\n\n"
                               f"已选择流程：{' → '.join(self.selected_processes)}\n"
                               f"当前进度：{current_process}\n\n"
                               f"请选择操作：")

                # 添加三个按钮
                continue_btn = msg_box.addButton("继续处理", QMessageBox.ButtonRole.AcceptRole)
                restart_btn = msg_box.addButton("重新开始", QMessageBox.ButtonRole.DestructiveRole)
                cancel_btn = msg_box.addButton("取消", QMessageBox.ButtonRole.RejectRole)

                msg_box.setDefaultButton(continue_btn)
                msg_box.exec()

                clicked_button = msg_box.clickedButton()

                if clicked_button == continue_btn:
                    # 继续处理 - 直接跳转到流程页面
                    if self.current_process_index < len(self.selected_processes):
                        # 恢复当前页面的选择状态
                        self.restore_current_page_selections()
                        # 直接跳转到当前流程页面
                        self.show_current_process_page()
                    else:
                        QMessageBox.information(self, "提示", "所有流程已完成！")
                elif clicked_button == restart_btn:
                    # 重新开始 - 清除进度
                    self.clear_process_progress()
                # 如果是取消或关闭对话框，不做任何操作，保持原有进度

        except Exception as e:
            print(f"恢复流程进度失败: {e}")

    def restore_current_page_selections(self):
        """恢复当前页面的选择状态"""
        try:
            if not hasattr(self, 'current_work_order_id') or not self.current_work_order_id:
                return

            # 获取当前流程
            if self.current_process_index >= len(self.selected_processes):
                return

            current_process = self.selected_processes[self.current_process_index]
            process_key_map = {
                '规划': 'planning',
                '建设': 'construction',
                '维护': 'maintenance',
                '优化': 'optimization',
                '客户': 'customer'
            }

            process_key = process_key_map.get(current_process)
            if not process_key:
                return

            # 从数据库加载当前工单的数据
            page_selections = self.load_page_selections_from_database(process_key)

            # 这个方法会在show_current_process_page之后被调用
            # 实际的恢复逻辑会在流程页面创建后执行
            self.pending_page_selections = page_selections

        except Exception as e:
            print(f"恢复当前页面选择状态失败: {e}")

    def load_page_selections_from_database(self, process_key):
        """从数据库加载页面选择状态 - 只有当前工单有进行中流程时才加载"""
        try:
            # 首先检查当前工单是否有进行中的流程进度
            if not hasattr(self, 'current_work_order_id') or not self.current_work_order_id:
                print(f"⚠️ 没有当前工单ID，{process_key} 页面保持空白")
                return {}

            # 检查是否有进行中的流程进度
            progress = self.data_manager.get_process_progress(self.current_work_order_id)
            if not progress or progress.get('status') != 'in_progress' or progress.get('work_order_id') != self.current_work_order_id:
                print(f"⚠️ 工单 {self.current_work_order_id} 没有进行中的流程进度，{process_key} 页面保持空白")
                return {}

            # 定义数据库列到选择项的映射关系
            column_to_selection = {
                'planning': {
                    'planning_big_network': 'big_network',
                    'planning_non_big_network': 'non_big_network'
                },
                'construction': {
                    'construction_property': 'property',
                    'construction_transmission': 'transmission',
                    'construction_power': 'power',
                    'construction_tower': 'tower',
                    'construction_equipment': 'equipment'
                },
                'maintenance': {
                    'maintenance_sporadic': 'sporadic',
                    'maintenance_outsourced': 'outsourced'
                },
                'optimization': {
                    'optimization_antenna': 'antenna',
                    'optimization_backend': 'backend'
                },
                'customer': {
                    'customer_field_test': 'field_test',
                    'customer_communication': 'communication'
                }
            }

            if process_key not in column_to_selection:
                return {}

            # 从数据库获取工单数据
            conn = self.data_manager.db.get_connection()
            cursor = conn.cursor()

            columns = list(column_to_selection[process_key].keys())
            column_list = ', '.join(columns)

            cursor.execute(f'''
                SELECT {column_list}
                FROM work_orders
                WHERE id = ?
            ''', (self.current_work_order_id,))

            result = cursor.fetchone()
            conn.close()

            if not result:
                return {}

            # 构建页面选择状态
            page_selections = {}

            for i, column in enumerate(columns):
                selection_key = column_to_selection[process_key][column]
                value = result[i]
                if value == '是':
                    page_selections[selection_key] = '是'
                elif value == '否':
                    page_selections[selection_key] = '否'

            print(f"✅ 从数据库加载工单 {self.current_work_order_id} 的 {process_key} 流程选择状态: {page_selections}")
            return page_selections

        except Exception as e:
            print(f"从数据库加载页面选择状态失败: {e}")
            return {}

    def update_process_selection_ui(self):
        """更新流程选择界面显示"""
        try:
            if not hasattr(self, 'selected_processes') or not self.selected_processes:
                return

            # 更新复选框状态
            process_key_map = {
                '规划': 'planning',
                '建设': 'construction',
                '维护': 'maintenance',
                '优化': 'optimization',
                '客户': 'customer'
            }

            # 先清空所有选择
            for checkbox in self.process_checkboxes.values():
                checkbox.setChecked(False)

            # 设置选中的流程
            for process_name in self.selected_processes:
                process_key = process_key_map.get(process_name)
                if process_key and process_key in self.process_checkboxes:
                    self.process_checkboxes[process_key].setChecked(True)

            # 更新原因选择
            if hasattr(self, 'selected_process_details'):
                for detail in self.selected_process_details:
                    for process_name, process_key in process_key_map.items():
                        if detail.startswith(f"{process_name}:") and process_key in self.process_reason_combos:
                            # 提取原因
                            reason_part = detail.split(': ', 1)[1]
                            reason = reason_part.split(' (')[0]  # 去掉天数部分

                            combo = self.process_reason_combos[process_key]
                            # 查找并设置对应的原因
                            for i in range(combo.count()):
                                if combo.itemText(i) == reason:
                                    combo.setCurrentIndex(i)
                                    break

        except Exception as e:
            print(f"更新流程选择界面失败: {e}")

    def clear_process_progress(self):
        """清除流程进度"""
        try:
            # 清除内存中的进度数据
            if hasattr(self, 'selected_processes'):
                delattr(self, 'selected_processes')
            if hasattr(self, 'selected_process_details'):
                delattr(self, 'selected_process_details')
            if hasattr(self, 'current_process_index'):
                delattr(self, 'current_process_index')
            if hasattr(self, 'process_results'):
                delattr(self, 'process_results')

            # 清除数据库中的进度记录
            if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
                self.data_manager.delete_process_progress(self.current_work_order_id)

            # 重置界面
            for checkbox in self.process_checkboxes.values():
                checkbox.setChecked(False)
            for combo in self.process_reason_combos.values():
                combo.setCurrentIndex(0)

        except Exception as e:
            print(f"清除流程进度失败: {e}")



    def show_transit_preview_context_menu(self, position):
        """显示在途数据预览右键菜单"""
        if self.transit_table.itemAt(position) is None:
            return

        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 3px;
            }
            QMenu::item:selected {
                background-color: #667eea;
                color: white;
            }
        """)

        # 查看详细操作
        detail_action = menu.addAction("🔍 查看详细操作")
        detail_action.triggered.connect(lambda: self.show_transit_detail_page(self.transit_table.itemAt(position))) # type: ignore

        menu.exec(self.transit_table.mapToGlobal(position))

    def update_process_stats(self):
        """更新处理页面的统计信息 - 显示流程进度统计"""
        if not self.current_data:
            if hasattr(self, 'process_stats_label'):
                self.process_stats_label.setText("暂无数据")
            return

        try:
            # 统计各个流程进度的工单数量
            process_stats = self.get_process_progress_stats()

            if hasattr(self, 'process_stats_label'):
                # 格式化显示流程统计信息
                stats_text = f"未开始: {process_stats['未开始']} | 规划: {process_stats['规划']} | 建设: {process_stats['建设']} | 维护: {process_stats['维护']} | 优化: {process_stats['优化']} | 客户: {process_stats['客户']} | 已完成: {process_stats['已完成']}"
                self.process_stats_label.setText(stats_text)

        except Exception as e:
            if hasattr(self, 'process_stats_label'):
                self.process_stats_label.setText(f"统计出错: {str(e)}")

    def get_process_progress_stats(self):
        """获取流程进度统计"""
        stats = {
            '未开始': 0,
            '规划': 0,
            '建设': 0,
            '维护': 0,
            '优化': 0,
            '客户': 0,
            '已完成': 0
        }

        if not self.current_data:
            return stats

        try:
            # 只统计在途数据的流程进度
            archive_column = 16  # Q列的索引

            for row_idx, row in enumerate(self.current_data[2:], start=3):  # 从第3行开始
                if len(row) > archive_column:
                    archive_status = str(row[archive_column]).strip() if row[archive_column] else ""
                    # 只统计在途数据
                    if not archive_status or archive_status.lower() in ['在途', '未归档', '0', '否', 'no', 'false', '']:
                        # 获取该行的流程进度
                        progress_text = self.get_process_progress_text(row_idx)

                        if progress_text == "未开始":
                            stats['未开始'] += 1
                        elif progress_text == "已完成":
                            stats['已完成'] += 1
                        elif "规划进行中" in progress_text:
                            stats['规划'] += 1
                        elif "建设进行中" in progress_text:
                            stats['建设'] += 1
                        elif "维护进行中" in progress_text:
                            stats['维护'] += 1
                        elif "优化进行中" in progress_text:
                            stats['优化'] += 1
                        elif "客户进行中" in progress_text:
                            stats['客户'] += 1

        except Exception as e:
            print(f"获取流程进度统计时出错: {e}")

        return stats

    def update_transit_stats(self):
        """更新在途数据页面的统计信息"""
        if not self.current_data:
            self.transit_stats_label.setText("暂无数据")
            if hasattr(self, 'process_stats_label'):
                self.process_stats_label.setText("未开始: 0 | 规划: 0 | 建设: 0 | 维护: 0 | 优化: 0 | 客户: 0 | 已完成: 0")
            return

        try:
            # 统计在途数据
            archive_column = 16  # Q列的索引
            transit_count = 0
            total_count = len(self.current_data) - 2  # 减去表头行

            for row in self.current_data[2:]:  # 从第3行开始
                if len(row) > archive_column:
                    archive_status = str(row[archive_column]).strip() if row[archive_column] else ""
                    if not archive_status or archive_status.lower() in ['在途', '未归档', '0', '否', 'no', 'false', '']:
                        transit_count += 1

            # 更新在途数据页面的统计标签
            self.transit_stats_label.setText(
                f"总数据: {total_count} 条 | 在途数据: {transit_count} 条 | 已归档: {total_count - transit_count} 条"
            )

            # 如果存在数据概览卡片中的统计标签，更新为流程进度统计
            if hasattr(self, 'process_stats_label'):
                process_stats = self.get_process_progress_stats()
                stats_text = f"未开始: {process_stats['未开始']} | 规划: {process_stats['规划']} | 建设: {process_stats['建设']} | 维护: {process_stats['维护']} | 优化: {process_stats['优化']} | 客户: {process_stats['客户']} | 已完成: {process_stats['已完成']}"
                self.process_stats_label.setText(stats_text)

        except Exception as e:
            self.transit_stats_label.setText(f"统计出错: {str(e)}")
            if hasattr(self, 'process_stats_label'):
                self.process_stats_label.setText(f"流程统计出错: {str(e)}")





    def analyze_all_numbers(self):
        """统计所有号码在I列中出现的次数"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "请先导入Excel文件")
            return

        try:
            # 更新状态指示器和提示信息
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setText("●")
                self.status_indicator.setStyleSheet("color: #ffc107;")  # 黄色表示处理中

            self.result_label.setText("🔄 正在统计所有号码数据，请稍候...")
            self.result_label.setStyleSheet("""
                QLabel {
                    color: #0d6efd;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(13, 110, 253, 0.1), stop:1 rgba(13, 110, 253, 0.05));
                    border: 1px solid rgba(13, 110, 253, 0.2);
                    border-radius: 12px;
                    padding: 20px;
                    min-height: 60px;
                    font-weight: 500;
                }
            """)
            QApplication.processEvents()  # 更新UI

            # 统计号码在I列中出现的次数
            number_counts = {}
            i_column_index = 8  # I列的索引（从0开始）
            total_rows = 0
            valid_numbers = 0

            # 遍历数据（跳过前两行表头）
            for row_idx, row in enumerate(self.current_data[2:], start=3):
                if len(row) > i_column_index:
                    total_rows += 1
                    cell_value = str(row[i_column_index]).strip() if row[i_column_index] else ""
                    if cell_value:  # 只统计非空值
                        valid_numbers += 1
                        if cell_value in number_counts:
                            number_counts[cell_value] += 1
                        else:
                            number_counts[cell_value] = 1

            # 按出现次数降序排序
            sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)

            # 更新统计信息
            self.number_stats_label.setText(f"共统计 {valid_numbers} 个有效号码，{len(number_counts)} 个不同号码")

            # 显示统计结果
            if sorted_numbers:
                # 更新状态指示器为成功状态
                if hasattr(self, 'status_indicator'):
                    self.status_indicator.setText("●")
                    self.status_indicator.setStyleSheet("color: #28a745;")  # 绿色表示成功

                self.result_label.setText(f"✅ 统计完成！共找到 {len(number_counts)} 个不同号码，按出现次数降序排列")
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #28a745;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(40, 167, 69, 0.1), stop:1 rgba(40, 167, 69, 0.05));
                        border: 1px solid rgba(40, 167, 69, 0.2);
                        border-radius: 12px;
                        padding: 20px;
                        min-height: 60px;
                        font-weight: 500;
                    }
                """)

                # 显示详细结果表格
                self.show_number_stats_results(sorted_numbers)
            else:
                # 更新状态指示器为错误状态
                if hasattr(self, 'status_indicator'):
                    self.status_indicator.setText("●")
                    self.status_indicator.setStyleSheet("color: #dc3545;")  # 红色表示错误

                self.result_label.setText("❌ 未找到任何有效号码数据")
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #dc3545;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(220, 53, 69, 0.1), stop:1 rgba(220, 53, 69, 0.05));
                        border: 1px solid rgba(220, 53, 69, 0.2);
                        border-radius: 12px;
                        padding: 20px;
                        min-height: 60px;
                        font-weight: 500;
                    }
                """)
                self.number_stats_table.setVisible(False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"统计过程中出错: {str(e)}")

    def show_number_stats_results(self, sorted_numbers):
        """显示号码统计结果的详细表格"""
        if not sorted_numbers:
            self.number_stats_table.setVisible(False)
            return

        # 设置表格列数和表头
        headers = ["序号", "号码", "出现次数", "占比(%)"]
        self.number_stats_table.setColumnCount(len(headers))
        self.number_stats_table.setHorizontalHeaderLabels(headers)
        self.number_stats_table.setRowCount(len(sorted_numbers))

        # 计算总次数用于计算百分比
        total_count = sum(count for _, count in sorted_numbers)

        # 填充数据
        for row_idx, (number, count) in enumerate(sorted_numbers):
            # 序号
            self.number_stats_table.setItem(row_idx, 0, QTableWidgetItem(str(row_idx + 1)))

            # 号码
            self.number_stats_table.setItem(row_idx, 1, QTableWidgetItem(number))

            # 出现次数
            self.number_stats_table.setItem(row_idx, 2, QTableWidgetItem(str(count)))

            # 占比
            percentage = (count / total_count) * 100 if total_count > 0 else 0
            self.number_stats_table.setItem(row_idx, 3, QTableWidgetItem(f"{percentage:.2f}"))

        # 调整列宽并显示表格
        self.number_stats_table.resizeColumnsToContents()
        self.number_stats_table.setVisible(True)

        # 启用排序
        self.number_stats_table.setSortingEnabled(True)

    def filter_number_table(self):
        """根据搜索框内容过滤号码统计表格"""
        if not hasattr(self, 'number_stats_table') or not self.number_stats_table.isVisible():
            return

        search_text = self.search_input.text().strip().lower()

        # 如果搜索框为空，显示所有行
        if not search_text:
            for row in range(self.number_stats_table.rowCount()):
                self.number_stats_table.setRowHidden(row, False)
            return

        # 否则，只显示包含搜索文本的行
        for row in range(self.number_stats_table.rowCount()):
            number_item = self.number_stats_table.item(row, 1)  # 号码列
            if number_item:
                number_text = number_item.text().lower()
                self.number_stats_table.setRowHidden(row, search_text not in number_text)

    def refresh_number_stats(self):
        """刷新号码统计数据"""
        if hasattr(self, 'search_input'):
            self.search_input.clear()
        self.analyze_all_numbers()

    def export_number_stats(self):
        """导出号码统计结果到Excel"""
        if not hasattr(self, 'number_stats_table') or not self.number_stats_table.isVisible():
            QMessageBox.warning(self, "警告", "请先进行号码统计分析")
            return

        if self.number_stats_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有可导出的统计数据")
            return

        try:
            # 选择保存文件路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出号码统计结果",
                f"号码统计结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel文件 (*.xlsx)"
            )

            if not file_path:
                return

            # 创建工作簿
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            wb = Workbook()
            ws = wb.active
            ws.title = "号码统计结果"

            # 设置表头
            headers = ["序号", "号码", "出现次数", "占比(%)"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="667EEA", end_color="667EEA", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = Border(
                    left=Side(style="thin"),
                    right=Side(style="thin"),
                    top=Side(style="thin"),
                    bottom=Side(style="thin")
                )

            # 填充数据
            for row in range(self.number_stats_table.rowCount()):
                for col in range(self.number_stats_table.columnCount()):
                    item = self.number_stats_table.item(row, col)
                    if item:
                        cell = ws.cell(row=row+2, column=col+1, value=item.text())
                        cell.alignment = Alignment(horizontal="center", vertical="center")
                        cell.border = Border(
                            left=Side(style="thin"),
                            right=Side(style="thin"),
                            top=Side(style="thin"),
                            bottom=Side(style="thin")
                        )

                        # 为数据行添加交替背景色
                        if row % 2 == 1:
                            cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存文件
            wb.save(file_path)

            QMessageBox.information(self, "成功", f"号码统计结果已导出到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def show_month_analysis_page(self):
        """显示月份分析页面"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "请先导入Excel文件")
            return

        # 分析数据并准备图表
        self.analyze_month_data()
        self.stacked_widget.setCurrentWidget(self.month_analysis_page)

    def show_archive_analysis_page(self):
        """显示归档时长分析页面"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "请先导入Excel文件")
            return

        # 分析归档时长数据并准备图表
        self.analyze_archive_data()
        self.stacked_widget.setCurrentWidget(self.archive_analysis_page)

    def show_reason_analysis_page(self):
        """显示原因分析页面"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "请先导入Excel文件")
            return

        # 分析原因数据并准备图表
        self.analyze_reason_data()
        self.stacked_widget.setCurrentWidget(self.reason_analysis_page)

    def show_region_analysis_page(self):
        """显示区域分析页面"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "请先导入Excel文件")
            return

        # 分析区域数据并准备图表
        self.analyze_region_data()
        self.stacked_widget.setCurrentWidget(self.region_analysis_page)

    def show_data_management_page(self):
        """显示数据管理页面"""
        # 更新按钮状态
        self.update_sidebar_buttons(self.data_mgmt_btn)

        # 切换到数据管理页面
        self.stacked_widget.setCurrentWidget(self.data_management_page)
        self.refresh_data_stats()
        self.load_upload_records()

    def refresh_data_stats(self):
        """刷新数据统计信息"""
        try:
            stats = self.data_manager.get_statistics()

            stats_text = (
                f"📊 总上传次数: {stats.get('total_uploads', 0)} | "
                f"📋 总工单数: {stats.get('total_orders', 0)} | "
                f"✅ 有效率: {stats.get('valid_ratio', 0):.1f}% | "
                f"🕒 最新上传: {stats.get('latest_upload', '无') or '无'}"
            )

            self.stats_info_label.setText(stats_text)

        except Exception as e:
            self.stats_info_label.setText(f"❌ 获取统计信息失败: {str(e)}")

    def load_upload_records(self):
        """加载上传记录到表格"""
        try:
            records = self.data_manager.get_upload_records()

            self.records_table.setRowCount(len(records))

            for row, record in enumerate(records):
                # 设置行高，确保按钮完全显示
                self.records_table.setRowHeight(row, 40)

                # ID
                self.records_table.setItem(row, 0, QTableWidgetItem(str(record['id'])))

                # 文件名
                self.records_table.setItem(row, 1, QTableWidgetItem(record['file_name']))

                # 上传时间
                upload_time = record['upload_time'][:19] if record['upload_time'] else ''
                self.records_table.setItem(row, 2, QTableWidgetItem(upload_time))

                # 总行数
                self.records_table.setItem(row, 3, QTableWidgetItem(str(record['total_rows'])))

                # 有效行数
                self.records_table.setItem(row, 4, QTableWidgetItem(str(record['valid_rows'])))

                # 状态
                status_text = "✅ 活跃" if record['status'] == 'active' else "❌ 已删除"
                self.records_table.setItem(row, 5, QTableWidgetItem(status_text))

                # 操作按钮 - 使用容器居中布局
                button_container = QWidget()
                container_layout = QHBoxLayout(button_container)
                container_layout.setContentsMargins(0, 0, 0, 0)
                container_layout.setSpacing(0)

                delete_btn = QPushButton("删除")
                delete_btn.setFixedHeight(28)
                delete_btn.setFixedWidth(60)
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        padding: 6px 8px;
                        border-radius: 4px;
                        font-weight: bold;
                        font-size: 12px;
                        font-family: 'Microsoft YaHei';
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, record_id=record['id']: self.delete_upload_record(record_id))

                # 添加弹性空间使按钮居中
                container_layout.addStretch()
                container_layout.addWidget(delete_btn)
                container_layout.addStretch()

                self.records_table.setCellWidget(row, 6, button_container)

                # 导出按钮
                export_container = QWidget()
                export_layout = QHBoxLayout(export_container)
                export_layout.setContentsMargins(0, 0, 0, 0)
                export_layout.setSpacing(0)

                export_btn = QPushButton("导出")
                export_btn.setFixedHeight(28)
                export_btn.setFixedWidth(60)
                export_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: none;
                        padding: 6px 8px;
                        border-radius: 4px;
                        font-weight: bold;
                        font-size: 12px;
                        font-family: 'Microsoft YaHei';
                    }
                    QPushButton:hover {
                        background-color: #218838;
                    }
                """)
                export_btn.clicked.connect(lambda checked, record_id=record['id']: self.export_upload_data(record_id))

                # 添加弹性空间使按钮居中
                export_layout.addStretch()
                export_layout.addWidget(export_btn)
                export_layout.addStretch()

                self.records_table.setCellWidget(row, 7, export_container)

            # 调整列宽
            self.records_table.resizeColumnsToContents()

            # 设置操作列的固定宽度，确保按钮完全显示
            self.records_table.setColumnWidth(6, 100)  # 删除列设置为100像素宽
            self.records_table.setColumnWidth(7, 100)  # 导出列设置为100像素宽

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载上传记录失败: {str(e)}")

    def export_upload_data(self, upload_id):
        """导出指定上传记录的数据"""
        try:
            # 获取该上传记录的所有工单数据
            conn = self.data_manager.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM work_orders
                WHERE upload_id = ? AND is_valid = 1
                ORDER BY row_number
            ''', (upload_id,))

            work_orders = cursor.fetchall()
            conn.close()

            if not work_orders:
                QMessageBox.warning(self, "提示", "该上传记录没有有效数据可导出")
                return

            # 第一步：选择Excel模板文件
            template_path, _ = QFileDialog.getOpenFileName(
                self, "选择Excel模板文件", "", "Excel文件 (*.xlsx *.xls)"
            )

            if not template_path:
                return

            # 第二步：选择保存位置
            save_path, _ = QFileDialog.getSaveFileName(
                self, "保存导出文件", f"导出数据_{upload_id}.xlsx", "Excel文件 (*.xlsx)"
            )

            if not save_path:
                return

            # 使用模板填写数据
            self.fill_template_with_data(template_path, work_orders, save_path)

            QMessageBox.information(
                self, "成功",
                f"数据导出成功！\n\n模板文件：{template_path}\n保存位置：{save_path}\n共导出 {len(work_orders)} 条记录"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出数据失败: {str(e)}")

    def export_all_data(self):
        """导出所有数据"""
        try:
            # 获取所有有效的工单数据
            all_work_orders = self.data_manager.get_all_work_orders()

            if not all_work_orders:
                QMessageBox.warning(self, "提示", "没有可导出的数据")
                return

            # 显示数据统计信息并确认导出
            stats = self.data_manager.get_statistics()
            total_uploads = stats.get('total_uploads', 0)
            total_orders = len(all_work_orders)

            reply = QMessageBox.question(
                self, "确认导出",
                f"即将导出全部数据：\n\n"
                f"📊 总上传次数：{total_uploads}\n"
                f"📋 总工单数：{total_orders}\n"
                f"📁 包含所有有效的工单记录\n\n"
                f"是否继续导出？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 第一步：选择Excel模板文件
            template_path, _ = QFileDialog.getOpenFileName(
                self, "选择Excel模板文件", "", "Excel文件 (*.xlsx *.xls)"
            )

            if not template_path:
                return

            # 第二步：选择保存位置
            save_path, _ = QFileDialog.getSaveFileName(
                self, "保存导出文件", f"全部数据导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx", "Excel文件 (*.xlsx)"
            )

            if not save_path:
                return

            # 使用模板填写数据
            self.fill_template_with_data(template_path, all_work_orders, save_path)

            QMessageBox.information(
                self, "成功",
                f"全部数据导出成功！\n\n模板文件：{template_path}\n保存位置：{save_path}\n共导出 {len(all_work_orders)} 条记录"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出全部数据失败: {str(e)}")

    def fill_template_with_data(self, template_path, work_orders, save_path):
        """使用Excel模板填写数据"""
        from openpyxl import load_workbook

        # 加载模板文件
        wb = load_workbook(template_path)
        ws = wb.active

        print(f"📋 使用模板文件: {template_path}")
        print(f"📊 准备填写 {len(work_orders)} 条数据")

        # 从第3行开始填写数据
        for row_idx, order in enumerate(work_orders, start=3):
            print(f"  📝 填写第 {row_idx} 行数据...")

            # 准备数据值（按A-AD列的顺序）
            data_values = [
                # A-Q列数据
                order['column_a'], order['column_b'], order['column_c'], order['column_d'],
                order['column_e'], order['column_f'], order['column_g'], order['column_h'],
                order['column_i'], order['column_j'], order['column_k'], order['column_l'],
                order['column_m'], order['column_n'], order['column_o'], order['column_p'],
                order['column_q'],

                # R-AD列数据（分组列）
                order['planning_big_network'], order['planning_non_big_network'],
                order['construction_property'], order['construction_transmission'],
                order['construction_power'], order['construction_tower'],
                order['construction_equipment'], order['maintenance_sporadic'],
                order['maintenance_outsourced'], order['optimization_antenna'],
                order['optimization_backend'], order['customer_field_test'],
                order['customer_communication']
            ]

            # 写入数据到对应列（A列=1, B列=2, ..., AD列=30）
            for col_idx, value in enumerate(data_values, 1):
                if col_idx <= 30:  # 确保不超过AD列
                    try:
                        # 处理不同类型的数据
                        if value is None:
                            cell_value = ""
                        elif isinstance(value, (int, float)):
                            cell_value = value
                        else:
                            cell_value = str(value)

                        ws.cell(row=row_idx, column=col_idx, value=cell_value) # type: ignore
                    except Exception as e:
                        print(f"    ⚠️ 写入第{row_idx}行第{col_idx}列时出错: {e}")
                        ws.cell(row=row_idx, column=col_idx, value="") # type: ignore

        # 保存填写后的文件
        wb.save(save_path)
        print(f"✅ 数据填写完成，保存到: {save_path}")

    def delete_upload_record(self, upload_id):
        """删除上传记录"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除上传记录 ID: {upload_id} 吗？\n\n此操作将删除该批次的所有工单数据，且无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                success = self.data_manager.delete_upload_data(upload_id)
                if success:
                    QMessageBox.information(self, "成功", "数据删除成功！")
                    self.refresh_data_stats()
                    self.load_upload_records()
                    # 重新加载当前数据
                    self.load_existing_data()
                else:
                    QMessageBox.warning(self, "失败", "数据删除失败！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除操作出错: {str(e)}")

    def load_existing_data(self):
        """加载已存在的数据"""
        try:
            # 从数据库获取所有工单数据（包括已归档和未归档）
            work_orders = self.data_manager.get_all_work_orders()

            if work_orders:
                # 转换为与原Excel格式兼容的数据结构
                self.current_data = self.convert_db_data_to_excel_format(work_orders)

                # 统计归档数据
                archived_orders = self.data_manager.get_archived_work_orders()
                archived_count = len(archived_orders)

                # 更新状态
                self.status_label.setText(f"已加载 {len(work_orders)} 条历史数据")
                self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")

                # 启用分析按钮
                if hasattr(self, 'analysis_buttons'):
                    self.set_analysis_buttons_enabled(True)
                if hasattr(self, 'archive_analysis_buttons'):
                    self.set_archive_analysis_buttons_enabled(True)

                # 如果在途数据相关组件已经初始化，则刷新数据
                if hasattr(self, 'transit_table') and hasattr(self, 'transit_stats_label'):
                    self.update_transit_stats()
                    self.refresh_transit_preview_data()

                print(f"从数据库加载了 {len(work_orders)} 条工单数据，其中 {archived_count} 条已归档")
            else:
                self.current_data = None
                self.status_label.setText("暂无历史数据")
                self.status_label.setStyleSheet("color: #6c757d; font-weight: bold;")

        except Exception as e:
            print(f"加载历史数据失败: {e}")
            self.status_label.setText("加载历史数据失败")
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")

    def convert_db_data_to_excel_format(self, work_orders):
        """将数据库数据转换为Excel格式"""
        try:
            # 创建表头行（模拟Excel的前两行）
            header_row1 = [''] * 30  # 第一行
            header_row2 = [''] * 30  # 第二行

            # 数据行
            data_rows = []

            for order in work_orders:
                row = []

                # A-Q列
                row.extend([
                    order.get('column_a'), order.get('column_b'), order.get('column_c'),
                    order.get('column_d'), order.get('column_e'), order.get('column_f'),
                    order.get('column_g'), order.get('column_h'), order.get('column_i'),
                    order.get('column_j'), order.get('column_k'), order.get('column_l'),
                    order.get('column_m'), order.get('column_n'), order.get('column_o'),
                    order.get('column_p'), order.get('column_q')
                ])

                # R-AD列（分组列）
                row.extend([
                    order.get('planning_big_network'), order.get('planning_non_big_network'),
                    order.get('construction_property'), order.get('construction_transmission'),
                    order.get('construction_power'), order.get('construction_tower'),
                    order.get('construction_equipment'), order.get('maintenance_sporadic'),
                    order.get('maintenance_outsourced'), order.get('optimization_antenna'),
                    order.get('optimization_backend'), order.get('customer_field_test'),
                    order.get('customer_communication')
                ])

                data_rows.append(row)

            # 组合所有数据
            all_data = [header_row1, header_row2] + data_rows

            print(f"转换了 {len(data_rows)} 行数据库数据为Excel格式")
            return all_data

        except Exception as e:
            print(f"数据格式转换失败: {e}")
            return None

    def analyze_month_data(self):
        """分析月份数据"""
        if not self.current_data or len(self.current_data) < 3:
            QMessageBox.warning(self, "警告", "数据不足，需要至少3行数据")
            return

        # 首先检查数据结构，找到第K列
        if len(self.current_data) == 0:
            QMessageBox.warning(self, "警告", "没有数据")
            return

        # 检查第一行数据，确定列数
        first_row = self.current_data[0] if self.current_data else []
        total_columns = len(first_row) if first_row else 0

        if total_columns < 11:  # K列是第11列
            QMessageBox.warning(self, "警告", f"数据列数不足，当前只有{total_columns}列，需要至少11列才能读取K列")
            return

        k_column_index = 10  # K列索引（从0开始）

        # 提取日期数据（从第3行开始，跳过前2行）
        date_data = []
        invalid_dates = []

        print(f"开始分析数据，总行数: {len(self.current_data)}")
        print(f"从第3行开始读取第{k_column_index + 1}列的数据...")

        for row_idx, row in enumerate(self.current_data[2:], start=3):  # 从第3行开始
            if len(row) > k_column_index:
                date_value = row[k_column_index]
                print(f"第{row_idx}行，第{k_column_index + 1}列的值: {date_value} (类型: {type(date_value)})")

                if date_value is not None:
                    try:
                        parsed_date = None

                        # 如果已经是datetime对象
                        if isinstance(date_value, datetime):
                            parsed_date = date_value
                            print(f"  -> 已是datetime对象: {parsed_date}")

                        # 如果是字符串，尝试解析
                        elif isinstance(date_value, str) and date_value.strip():
                            date_str = date_value.strip()
                            # 尝试多种日期格式
                            formats = [
                                '%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y',
                                '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S',
                                '%d-%m-%Y', '%d.%m.%Y', '%Y.%m.%d',
                                '%Y年%m月%d日', '%m月%d日', '%Y-%m', '%Y/%m'
                            ]

                            for fmt in formats:
                                try:
                                    parsed_date = datetime.strptime(date_str, fmt)
                                    print(f"  -> 成功解析: {parsed_date} (格式: {fmt})")
                                    break
                                except ValueError:
                                    continue

                        # 如果是数字（可能是Excel的日期序列号）
                        elif isinstance(date_value, (int, float)):
                            try:
                                # Excel日期从1900年1月1日开始计算
                                from datetime import timedelta
                                excel_epoch = datetime(1900, 1, 1)
                                # Excel有一个bug，认为1900年是闰年，所以需要减去2天
                                if date_value > 59:
                                    parsed_date = excel_epoch + timedelta(days=date_value - 2)
                                else:
                                    parsed_date = excel_epoch + timedelta(days=date_value - 1)
                                print(f"  -> Excel序列号解析: {parsed_date}")
                            except:
                                pass

                        if parsed_date:
                            date_data.append((parsed_date, row_idx))
                        else:
                            invalid_dates.append((row_idx, date_value))
                            print(f"  -> 无法解析的日期: {date_value}")

                    except Exception as e:
                        invalid_dates.append((row_idx, date_value))
                        print(f"  -> 解析出错: {e}")
                else:
                    print(f"  -> 空值")

        print(f"成功解析的日期数量: {len(date_data)}")
        print(f"无法解析的日期数量: {len(invalid_dates)}")

        if not date_data:
            error_msg = f"在第{k_column_index + 1}列（K列）中没有找到有效的日期数据。\n\n"
            error_msg += f"检查的行数: {len(self.current_data) - 2}\n"
            if invalid_dates:
                error_msg += f"无法解析的数据示例: {invalid_dates[:5]}\n\n"
            error_msg += "请确认:\n1. 第K列包含日期数据\n2. 日期格式正确\n3. 数据从第3行开始"
            QMessageBox.warning(self, "警告", error_msg)
            return

        # 按年份和月份统计
        self.year_month_stats = defaultdict(lambda: defaultdict(int))

        for date_obj, row_idx in date_data:
            year = date_obj.year
            month = date_obj.month
            self.year_month_stats[year][month] += 1

        print(f"统计结果: {dict(self.year_month_stats)}")

        # 更新年份下拉框
        self.year_combo.clear()
        years = sorted(self.year_month_stats.keys())
        for year in years:
            self.year_combo.addItem(str(year))

        # 数据解析完成，直接显示图表，不显示弹窗
        print(f"月份分析完成: 解析了 {len(date_data)} 条有效日期，涵盖年份: {min(years)} - {max(years)}")
        if invalid_dates:
            print(f"跳过了 {len(invalid_dates)} 条无效数据")

        # 如果有数据，显示第一年的图表
        if years:
            self.update_month_chart(str(years[0]))

    def analyze_archive_data(self):
        """分析归档时长数据"""
        if not self.current_data or len(self.current_data) < 3:
            QMessageBox.warning(self, "警告", "数据不足，需要至少3行数据")
            return

        # 检查数据结构
        if len(self.current_data) == 0:
            QMessageBox.warning(self, "警告", "没有数据")
            return

        # 检查列数
        first_row = self.current_data[0] if self.current_data else []
        total_columns = len(first_row) if first_row else 0

        if total_columns < 11:  # 需要J列(索引9)和K列(索引10)
            QMessageBox.warning(self, "警告", f"数据列数不足，当前只有{total_columns}列，需要至少11列才能读取J列和K列")
            return

        j_column_index = 9   # J列索引
        k_column_index = 10  # K列索引

        # 提取归档时长数据（从第3行开始）
        archive_days = []
        invalid_data = []
        special_cases = 0  # "无需现场测试"的情况

        print(f"开始分析归档时长数据，总行数: {len(self.current_data)}")
        print(f"从第3行开始读取第{j_column_index + 1}列(J列)和第{k_column_index + 1}列(K列)的数据...")

        for row_idx, row in enumerate(self.current_data[2:], start=3):  # 从第3行开始
            if len(row) > k_column_index:
                j_value = row[j_column_index]  # J列值
                k_value = row[k_column_index]  # K列值

                print(f"第{row_idx}行: J列={j_value}, K列={k_value}")

                try:
                    # 检查J列是否为"无需现场测试"
                    if j_value and isinstance(j_value, str) and "无需现场测试" in str(j_value):
                        archive_days.append(1)  # 设为1天
                        special_cases += 1
                        print(f"  -> 无需现场测试，设为1天")
                        continue

                    # 尝试解析J列和K列的日期
                    j_date = None
                    k_date = None

                    # 解析J列日期
                    if j_value is not None:
                        j_date = self.parse_date_value(j_value)

                    # 解析K列日期
                    if k_value is not None:
                        k_date = self.parse_date_value(k_value)

                    if j_date and k_date:
                        # 计算天数差 (K列 - J列)
                        days_diff = (k_date - j_date).days
                        if days_diff >= 0:  # 只记录非负数的天数差
                            archive_days.append(days_diff)
                            print(f"  -> 归档时长: {days_diff}天")
                        else:
                            print(f"  -> 负数天数差: {days_diff}天，跳过")
                            invalid_data.append((row_idx, f"负数天数差: {days_diff}"))
                    else:
                        invalid_data.append((row_idx, f"日期解析失败: J={j_value}, K={k_value}"))
                        print(f"  -> 日期解析失败")

                except Exception as e:
                    invalid_data.append((row_idx, f"处理出错: {e}"))
                    print(f"  -> 处理出错: {e}")

        print(f"成功计算的归档时长数量: {len(archive_days)}")
        print(f"特殊情况(无需现场测试): {special_cases}")
        print(f"无法处理的数据数量: {len(invalid_data)}")

        if not archive_days:
            error_msg = f"在J列和K列中没有找到有效的日期数据进行归档时长计算。\n\n"
            error_msg += f"检查的行数: {len(self.current_data) - 2}\n"
            if invalid_data:
                error_msg += f"无法处理的数据示例: {invalid_data[:5]}\n\n"
            error_msg += "请确认:\n1. J列和K列包含日期数据\n2. 日期格式正确\n3. K列日期晚于J列日期"
            QMessageBox.warning(self, "警告", error_msg)
            return

        # 生成归档时长分档统计
        self.create_archive_duration_chart(archive_days, special_cases, len(invalid_data))

        # 归档时长分析完成，直接显示图表，不显示弹窗
        print(f"归档时长分析完成: 计算了 {len(archive_days)} 条有效数据，特殊情况 {special_cases} 条")
        print(f"平均归档时长: {sum(archive_days)/len(archive_days):.1f} 天")
        if invalid_data:
            print(f"跳过了 {len(invalid_data)} 条无效数据")

    def analyze_reason_data(self):
        """分析原因数据"""
        if not self.current_data or len(self.current_data) < 3:
            QMessageBox.warning(self, "警告", "数据不足，需要至少3行数据")
            return

        # 检查数据结构
        if len(self.current_data) == 0:
            QMessageBox.warning(self, "警告", "没有数据")
            return

        # 检查列数 - P列是第16列
        first_row = self.current_data[0] if self.current_data else []
        total_columns = len(first_row) if first_row else 0

        if total_columns < 16:  # P列是第16列(索引15)
            QMessageBox.warning(self, "警告", f"数据列数不足，当前只有{total_columns}列，需要至少16列才能读取P列")
            return

        p_column_index = 15  # P列索引

        # 提取原因数据（从第3行开始）
        reasons = []
        empty_count = 0

        print(f"开始分析原因数据，总行数: {len(self.current_data)}")
        print(f"从第3行开始读取第{p_column_index + 1}列(P列)的数据...")

        for row_idx, row in enumerate(self.current_data[2:], start=3):  # 从第3行开始
            if len(row) > p_column_index:
                reason_value = row[p_column_index]

                if reason_value is not None and str(reason_value).strip():
                    reason_text = str(reason_value).strip()
                    reasons.append(reason_text)
                    print(f"第{row_idx}行: {reason_text}")
                else:
                    empty_count += 1
                    print(f"第{row_idx}行: 空值")

        print(f"成功读取的原因数量: {len(reasons)}")
        print(f"空值数量: {empty_count}")

        if not reasons:
            error_msg = f"在P列中没有找到有效的原因数据。\n\n"
            error_msg += f"检查的行数: {len(self.current_data) - 2}\n"
            error_msg += f"空值数量: {empty_count}\n\n"
            error_msg += "请确认:\n1. P列包含开单原因数据\n2. 数据从第3行开始\n3. 原因字段不为空"
            QMessageBox.warning(self, "警告", error_msg)
            return

        # 进行原因聚类分析
        reason_clusters = self.cluster_reasons(reasons)

        # 生成原因分析图表
        self.create_reason_analysis_chart(reason_clusters, empty_count)

        # 显示数据解析信息
        total_reasons = len(reason_clusters)
        total_count = sum(reason_clusters.values())
        most_common = max(reason_clusters.items(), key=lambda x: x[1])

        # 原因分析完成，直接显示图表，不显示弹窗
        print(f"原因分析完成: 分析了 {total_count} 条有效数据，识别出 {total_reasons} 种不同原因")
        print(f"最主要原因: {most_common[0]} ({most_common[1]} 次)")
        if empty_count > 0:
            print(f"跳过了 {empty_count} 条空值数据")

    def analyze_region_data(self):
        """分析区域数据 - 按年份分组"""
        if not self.current_data or len(self.current_data) < 3:
            QMessageBox.warning(self, "警告", "数据不足，需要至少3行数据")
            return

        # 检查数据结构
        if len(self.current_data) == 0:
            QMessageBox.warning(self, "警告", "没有数据")
            return

        # 检查列数 - K列是第11列，M列是第13列，N列是第14列
        first_row = self.current_data[0] if self.current_data else []
        total_columns = len(first_row) if first_row else 0

        if total_columns < 14:  # N列是第14列(索引13)
            QMessageBox.warning(self, "警告", f"数据列数不足，当前只有{total_columns}列，需要至少14列才能读取K、M、N列")
            return

        k_column_index = 10  # K列索引(解决时间)
        m_column_index = 12  # M列索引(经度)
        n_column_index = 13  # N列索引(纬度)

        # 按年份分组的坐标数据
        coordinates_by_year = defaultdict(list)
        invalid_count = 0
        date_parse_errors = 0

        print(f"开始分析区域数据，总行数: {len(self.current_data)}")
        print(f"从第3行开始读取第{k_column_index + 1}列(K列-解决时间)、第{m_column_index + 1}列(M列-经度)和第{n_column_index + 1}列(N列-纬度)的数据...")

        for row_idx, row in enumerate(self.current_data[2:], start=3):  # 从第3行开始
            if len(row) > n_column_index:
                k_value = row[k_column_index]  # K列解决时间
                lng_value = row[m_column_index]  # M列经度
                lat_value = row[n_column_index]  # N列纬度

                try:
                    # 首先解析日期获取年份
                    year = None
                    if k_value is not None:
                        parsed_date = self.parse_date_value(k_value)
                        if parsed_date:
                            year = parsed_date.year
                        else:
                            print(f"第{row_idx}行: 无法解析日期 - K列={k_value}")
                            date_parse_errors += 1
                            continue
                    else:
                        print(f"第{row_idx}行: K列日期为空")
                        date_parse_errors += 1
                        continue

                    # 然后处理经纬度数据
                    if lng_value is not None and lat_value is not None:
                        lng = float(str(lng_value).strip()) if str(lng_value).strip() else None
                        lat = float(str(lat_value).strip()) if str(lat_value).strip() else None

                        if lng is not None and lat is not None:
                            # 简单的经纬度范围验证（中国境内大致范围）
                            if 73 <= lng <= 135 and 18 <= lat <= 54:
                                coordinates_by_year[year].append((lng, lat, row_idx))
                                print(f"第{row_idx}行: {year}年 经度={lng}, 纬度={lat}")
                            else:
                                print(f"第{row_idx}行: 经纬度超出中国范围 - 经度={lng}, 纬度={lat}")
                                invalid_count += 1
                        else:
                            print(f"第{row_idx}行: 经纬度转换失败 - M列={lng_value}, N列={lat_value}")
                            invalid_count += 1
                    else:
                        print(f"第{row_idx}行: 经纬度空值 - M列={lng_value}, N列={lat_value}")
                        invalid_count += 1

                except Exception as e:
                    print(f"第{row_idx}行: 处理出错 - {e}")
                    invalid_count += 1

        # 计算总坐标数量
        total_coords = sum(len(coords) for coords in coordinates_by_year.values())

        print(f"成功读取的坐标数量: {total_coords}")
        print(f"无效数据数量: {invalid_count}")
        print(f"日期解析错误数量: {date_parse_errors}")

        if not coordinates_by_year:
            error_msg = f"在K、M、N列中没有找到有效的数据。\n\n"
            error_msg += f"检查的行数: {len(self.current_data) - 2}\n"
            error_msg += f"无效数据数量: {invalid_count}\n"
            error_msg += f"日期解析错误数量: {date_parse_errors}\n\n"
            error_msg += "请确认:\n1. K列包含日期数据\n2. M列包含经度数据\n3. N列包含纬度数据\n4. 经纬度为数字格式\n5. 坐标在中国境内范围"
            QMessageBox.warning(self, "警告", error_msg)
            return

        # 保存按年份分组的坐标数据
        self.current_coordinates_by_year = dict(coordinates_by_year)

        # 更新年份下拉框
        self.region_year_combo.clear()
        years = sorted(coordinates_by_year.keys())
        for year in years:
            self.region_year_combo.addItem(str(year))

        # 显示数据解析信息
        print(f"区域分析完成: 解析了 {total_coords} 个坐标点，涵盖年份: {min(years)} - {max(years)}")
        for year in years:
            year_coords = coordinates_by_year[year]
            print(f"  {year}年: {len(year_coords)} 个坐标点")
        if invalid_count > 0:
            print(f"跳过了 {invalid_count} 条无效数据")
        if date_parse_errors > 0:
            print(f"跳过了 {date_parse_errors} 条日期解析错误的数据")

        # 如果有数据，显示第一年的图表
        if years:
            self.update_region_chart(str(years[0]))

    def cluster_reasons(self, reasons):
        """对原因进行聚类分析"""
        from collections import Counter
        import re

        # 数据清洗和标准化
        cleaned_reasons = []

        for reason in reasons:
            # 转换为小写并去除多余空格
            cleaned = re.sub(r'\s+', ' ', reason.lower().strip())

            # 去除标点符号
            cleaned = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cleaned)

            if cleaned:
                cleaned_reasons.append(cleaned)

        # 统计频次
        reason_counter = Counter(cleaned_reasons)

        # 进一步聚类相似的原因
        clustered_reasons = {}
        processed = set()

        for reason, count in reason_counter.most_common():
            if reason in processed:
                continue

            # 查找相似的原因
            similar_reasons = [reason]
            similar_count = count

            for other_reason, other_count in reason_counter.items():
                if other_reason != reason and other_reason not in processed:
                    # 简单的相似度判断：包含关系或编辑距离
                    if (reason in other_reason or other_reason in reason or
                        self.calculate_similarity(reason, other_reason) > 0.8):
                        similar_reasons.append(other_reason)
                        similar_count += other_count
                        processed.add(other_reason)

            # 选择最具代表性的原因作为聚类名称
            cluster_name = max(similar_reasons, key=len)
            clustered_reasons[cluster_name] = similar_count
            processed.add(reason)

        # 只保留前15个最主要的原因，其余归为"其他"
        sorted_reasons = dict(sorted(clustered_reasons.items(), key=lambda x: x[1], reverse=True))

        if len(sorted_reasons) > 15:
            top_reasons = dict(list(sorted_reasons.items())[:15])
            other_count = sum(list(sorted_reasons.values())[15:])
            if other_count > 0:
                top_reasons["其他原因"] = other_count
            return top_reasons

        return sorted_reasons

    def create_interactive_map(self, coordinates, invalid_count):
        """创建交互式云南省地图"""
        try:
            # 清除之前的图表
            for i in reversed(range(self.region_chart_layout.count())):
                item = self.region_chart_layout.itemAt(i)
                if item and item.widget():
                    item.widget().setParent(None)

            # 检查是否安装了folium
            try:
                import folium
                print("✅ folium 可用")
            except ImportError as e:
                QMessageBox.warning(self, "提示",
                    "交互式地图需要安装folium库。\n"
                    "请运行以下命令安装：\n"
                    "pip install folium\n\n"
                    "现在将显示静态地图。")
                print(f"❌ folium 未安装: {e}")
                self.map_type_combo.setCurrentText("静态地图")
                self.create_region_analysis_chart(coordinates, invalid_count)
                return

            # 尝试使用QWebEngineView，如果失败则使用浏览器打开
            use_web_engine = False
            try:
                from PyQt6.QtWebEngineWidgets import QWebEngineView
                from PyQt6.QtCore import QUrl
                use_web_engine = True
                print("✅ PyQt6-WebEngine 可用，将在应用内显示地图")
            except (ImportError, Exception) as e:
                print(f"⚠️ PyQt6-WebEngine 不可用: {e}")
                print("将使用系统默认浏览器打开地图")

            # 创建云南省中心的地图
            yunnan_center = [25.0, 102.0]  # 云南省大致中心位置

            # 创建folium地图
            m = folium.Map(
                location=yunnan_center,
                zoom_start=7,
                tiles='OpenStreetMap',
                control_scale=True,
                prefer_canvas=True
            )

            # 添加不同的地图图层
            folium.TileLayer(
                'Stamen Terrain',
                name='地形图',
                attr='Map tiles by Stamen Design, CC BY 3.0 — Map data © OpenStreetMap contributors'
            ).add_to(m)
            folium.TileLayer(
                'Stamen Toner',
                name='黑白地图',
                attr='Map tiles by Stamen Design, CC BY 3.0 — Map data © OpenStreetMap contributors'
            ).add_to(m)
            folium.TileLayer(
                'CartoDB positron',
                name='简洁地图',
                attr='© CartoDB, © OpenStreetMap contributors'
            ).add_to(m)

            # 添加工单位置标记
            if coordinates:
                # 创建标记聚类
                from folium.plugins import MarkerCluster, HeatMap

                # 标记聚类
                marker_cluster = MarkerCluster(name='工单位置').add_to(m)

                # 热力图数据
                heat_data = []

                for i, (lng, lat, info) in enumerate(coordinates):
                    # 添加标记到聚类
                    popup_text = f"""
                    <div style='font-family: Microsoft YaHei; font-size: 12px;'>
                        <b>工单 #{i+1}</b><br>
                        <b>经度:</b> {lng:.6f}<br>
                        <b>纬度:</b> {lat:.6f}<br>
                        <b>详情:</b> {info if info else '无详细信息'}
                    </div>
                    """

                    folium.Marker(
                        location=[lat, lng],
                        popup=folium.Popup(popup_text, max_width=300),
                        tooltip=f"工单 #{i+1}",
                        icon=folium.Icon(color='red', icon='info-sign')
                    ).add_to(marker_cluster)

                    # 添加到热力图数据
                    heat_data.append([lat, lng])

                # 添加热力图图层
                if heat_data:
                    HeatMap(heat_data, name='密度热力图', radius=15, blur=10).add_to(m)

            # 添加图层控制
            folium.LayerControl().add_to(m)

            # 添加全屏插件
            from folium.plugins import Fullscreen
            Fullscreen().add_to(m)

            # 添加测量工具
            from folium.plugins import MeasureControl
            MeasureControl().add_to(m)

            # 保存地图到临时HTML文件
            import tempfile
            import os
            import webbrowser

            temp_dir = tempfile.gettempdir()
            map_file = os.path.join(temp_dir, 'yunnan_interactive_map.html')
            m.save(map_file)

            # 根据WebEngine可用性选择显示方式
            if use_web_engine:
                try:
                    # 创建Web视图显示地图
                    web_view = QWebEngineView()
                    web_view.setMinimumHeight(500)
                    web_view.load(QUrl.fromLocalFile(map_file))

                    # 添加地图说明
                    info_widget = QWidget()
                    info_layout = QVBoxLayout(info_widget)
                    info_layout.setContentsMargins(10, 5, 10, 5)

                    map_info = QLabel("""
🗺️ <b>交互式地图使用说明：</b><br>
• <b>缩放：</b> 鼠标滚轮或点击 +/- 按钮<br>
• <b>拖拽：</b> 按住鼠标左键拖动地图<br>
• <b>标记：</b> 点击红色标记查看工单详情<br>
• <b>图层：</b> 右上角可切换不同地图样式<br>
• <b>全屏：</b> 点击全屏按钮获得更好体验<br>
• <b>测量：</b> 使用测量工具计算距离和面积
                    """)
                    map_info.setFont(QFont("Microsoft YaHei", 9))
                    map_info.setStyleSheet("""
                        QLabel {
                            background: rgba(102, 126, 234, 0.1);
                            border: 1px solid rgba(102, 126, 234, 0.3);
                            border-radius: 8px;
                            padding: 10px;
                            color: #333;
                        }
                    """)
                    map_info.setWordWrap(True)
                    info_layout.addWidget(map_info)

                    # 添加到布局
                    self.region_chart_layout.addWidget(info_widget)
                    self.region_chart_layout.addWidget(web_view, 1)

                    print(f"✅ 交互式地图已在应用内显示")

                except Exception as e:
                    print(f"❌ WebEngine显示失败: {e}")
                    use_web_engine = False

            if not use_web_engine:
                # 使用系统默认浏览器打开地图
                try:
                    webbrowser.open(f'file://{map_file}')

                    # 在应用内显示提示信息
                    browser_info = QLabel(f"""
🗺️ <b>交互式地图已在浏览器中打开</b><br><br>
📁 <b>地图文件位置：</b><br>
{map_file}<br><br>
🎯 <b>功能说明：</b><br>
• <b>缩放：</b> 鼠标滚轮或点击 +/- 按钮<br>
• <b>拖拽：</b> 按住鼠标左键拖动地图<br>
• <b>标记：</b> 点击红色标记查看工单详情<br>
• <b>图层：</b> 右上角可切换不同地图样式<br>
• <b>全屏：</b> 点击全屏按钮获得更好体验<br>
• <b>测量：</b> 使用测量工具计算距离和面积<br><br>
💡 <b>提示：</b> 如果浏览器没有自动打开，请手动打开上述文件路径
                    """)
                    browser_info.setFont(QFont("Microsoft YaHei", 10))
                    browser_info.setStyleSheet("""
                        QLabel {
                            background: rgba(102, 126, 234, 0.1);
                            border: 2px solid rgba(102, 126, 234, 0.3);
                            border-radius: 12px;
                            padding: 20px;
                            color: #333;
                            margin: 10px;
                        }
                    """)
                    browser_info.setWordWrap(True)
                    browser_info.setAlignment(Qt.AlignmentFlag.AlignTop)

                    # 添加到布局
                    self.region_chart_layout.addWidget(browser_info)

                    print(f"✅ 交互式地图已在浏览器中打开: {map_file}")

                except Exception as e:
                    print(f"❌ 浏览器打开失败: {e}")
                    QMessageBox.warning(self, "错误", f"无法打开交互式地图: {e}")
                    self.map_type_combo.setCurrentText("静态地图")
                    self.create_region_analysis_chart(coordinates, invalid_count)
                    return

            # 更新统计信息
            self.update_region_stats(coordinates)

        except Exception as e:
            error_msg = f"创建交互式地图时出错: {str(e)}"
            print(error_msg)
            QMessageBox.warning(self, "警告", f"{error_msg}\n\n将显示静态地图。")
            self.map_type_combo.setCurrentText("静态地图")
            self.create_region_analysis_chart(coordinates, invalid_count)

    def update_region_stats(self, coordinates):
        """更新区域统计信息"""
        try:
            total_coords = len(coordinates)

            if coordinates:
                lng_range = f"{min(coord[0] for coord in coordinates):.3f} - {max(coord[0] for coord in coordinates):.3f}"
                lat_range = f"{min(coord[1] for coord in coordinates):.3f} - {max(coord[1] for coord in coordinates):.3f}"

                # 计算工单密度最高的区域
                from collections import Counter
                grid_coords = []
                for lng, lat, _ in coordinates:
                    grid_lng = round(lng, 1)
                    grid_lat = round(lat, 1)
                    grid_coords.append(f"{grid_lng},{grid_lat}")

                grid_counter = Counter(grid_coords)
                top_grid = grid_counter.most_common(1)[0] if grid_counter else ("无", 0)

                self.region_stats_label.setText(
                    f"总计: {total_coords} 个工单 | "
                    f"经度范围: {lng_range} | "
                    f"纬度范围: {lat_range} | "
                    f"密度最高区域: {top_grid[0]} ({top_grid[1]} 个工单)"
                )
            else:
                self.region_stats_label.setText("暂无工单数据")
        except Exception as e:
            print(f"更新统计信息时出错: {e}")

    def calculate_similarity(self, str1, str2):
        """计算两个字符串的相似度"""
        if not str1 or not str2:
            return 0

        # 简单的Jaccard相似度
        set1 = set(str1)
        set2 = set(str2)

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0

    def load_yunnan_map(self):
        """加载云南省地图数据（带缓存）"""
        if self.yunnan_map_cache is not None:
            print("使用缓存的云南省地图数据")
            return self.yunnan_map_cache

        try:
            # 获取yunnan.json文件路径
            json_path = os.path.join(os.path.dirname(__file__), 'yunnan.json')

            if not os.path.exists(json_path):
                print(f"未找到云南省地图文件: {json_path}")
                return None

            print("正在加载云南省地图数据...")
            with open(json_path, 'r', encoding='utf-8') as f:
                geojson_data = json.load(f)

            # 解析GeoJSON数据
            polygons = []
            for feature in geojson_data['features']:
                geometry = feature['geometry']
                if geometry['type'] == 'MultiPolygon':
                    for polygon_coords in geometry['coordinates']:
                        for ring in polygon_coords:
                            # 转换坐标格式
                            coords = [(point[0], point[1]) for point in ring]
                            polygons.append(coords)
                elif geometry['type'] == 'Polygon':
                    for ring in geometry['coordinates']:
                        coords = [(point[0], point[1]) for point in ring]
                        polygons.append(coords)

            self.yunnan_map_cache = polygons
            print(f"云南省地图数据加载完成，包含 {len(polygons)} 个多边形")
            return polygons

        except Exception as e:
            print(f"加载云南省地图数据失败: {e}")
            return None

    def parse_date_value(self, date_value):
        """解析日期值，返回datetime对象"""
        try:
            # 如果已经是datetime对象
            if isinstance(date_value, datetime):
                return date_value

            # 如果是字符串，尝试解析
            elif isinstance(date_value, str) and date_value.strip():
                date_str = date_value.strip()
                # 尝试多种日期格式
                formats = [
                    '%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y',
                    '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S',
                    '%d-%m-%Y', '%d.%m.%Y', '%Y.%m.%d',
                    '%Y年%m月%d日', '%m月%d日', '%Y-%m', '%Y/%m'
                ]

                for fmt in formats:
                    try:
                        return datetime.strptime(date_str, fmt)
                    except ValueError:
                        continue

            # 如果是数字（可能是Excel的日期序列号）
            elif isinstance(date_value, (int, float)):
                # Excel日期从1900年1月1日开始计算
                from datetime import timedelta
                excel_epoch = datetime(1900, 1, 1)
                # Excel有一个bug，认为1900年是闰年，所以需要减去2天
                if date_value > 59:
                    return excel_epoch + timedelta(days=date_value - 2)
                else:
                    return excel_epoch + timedelta(days=date_value - 1)

        except Exception as e:
            print(f"日期解析错误: {e}")

        return None

    def create_region_analysis_chart(self, coordinates, invalid_count):
        """创建云南省地图区域分析图表"""
        try:
            # 清除之前的图表
            for i in reversed(range(self.region_chart_layout.count())):
                item = self.region_chart_layout.itemAt(i)
                if item and item.widget():
                    item.widget().setParent(None) # type: ignore

            # 加载云南省地图数据
            yunnan_polygons = self.load_yunnan_map()
            if not yunnan_polygons:
                QMessageBox.warning(self, "警告", "无法加载云南省地图数据，请确保yunnan.json文件存在")
                return

            # 创建matplotlib图表 - 充分利用空间
            fig = Figure(figsize=(12, 7), dpi=90)  # 增大图表尺寸
            canvas = FigureCanvas(fig)
            canvas.setMaximumHeight(550)  # 增大最大高度
            canvas.setFixedHeight(550)  # 固定高度

            # 设置中文字体
            import matplotlib
            import matplotlib.font_manager as fm
            import platform

            def setup_chinese_font():
                system = platform.system()
                chinese_fonts = []

                if system == "Windows":
                    default_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
                elif system == "Darwin":  # macOS
                    default_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
                else:  # Linux
                    default_fonts = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans CN']

                available_fonts = [f.name for f in fm.fontManager.ttflist]

                for font_name in default_fonts:
                    if font_name in available_fonts:
                        chinese_fonts.append(font_name)

                for font in fm.fontManager.ttflist:
                    font_name = font.name
                    if any(keyword in font_name.lower() for keyword in
                          ['yahei', 'simhei', 'simsun', 'pingfang', 'heiti', 'songti', 'kaiti', 'noto', 'source han']):
                        if font_name not in chinese_fonts:
                            chinese_fonts.append(font_name)

                return chinese_fonts[:5]

            chinese_fonts = setup_chinese_font()

            try:
                if chinese_fonts:
                    matplotlib.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans', 'Arial']
                else:
                    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']

                matplotlib.rcParams['axes.unicode_minus'] = False
            except Exception as e:
                print(f"设置matplotlib字体时出错: {e}")
                # 使用默认设置
                matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                matplotlib.rcParams['axes.unicode_minus'] = False

            # 创建云南省地图
            ax = fig.add_subplot(1, 1, 1)

            # 绘制云南省边界
            patches = []
            for polygon_coords in yunnan_polygons:
                if len(polygon_coords) > 2:  # 确保多边形有效
                    polygon = Polygon(polygon_coords, closed=True)
                    patches.append(polygon)

            # 创建多边形集合
            p = PatchCollection(patches, facecolors='lightblue', edgecolors='navy', alpha=0.3, linewidths=0.5)
            ax.add_collection(p) # type: ignore

            # 绘制工单位置点
            if coordinates:
                lngs = [coord[0] for coord in coordinates]
                lats = [coord[1] for coord in coordinates]

                # 创建散点图 - 在云南省地图上显示工单位置
                scatter = ax.scatter(lngs, lats, c='red', s=50, alpha=0.8,  # 适中的散点大小
                                   edgecolors='darkred', linewidth=0.8, zorder=5)  # 适中的边框线宽

                # 设置地图标题和标签 - 适中样式
                try:
                    ax.set_title('云南省工单分布地图', fontsize=16, fontweight='bold', pad=15)  # 增大字体和padding
                    ax.set_xlabel('经度', fontsize=12)  # 增大字体
                    ax.set_ylabel('纬度', fontsize=12)  # 增大字体
                except:
                    ax.set_title('Yunnan Province Work Order Distribution', fontsize=16, fontweight='bold', pad=15)
                    ax.set_xlabel('Longitude', fontsize=12)
                    ax.set_ylabel('Latitude', fontsize=12)

                # 设置坐标轴范围（云南省大致范围）
                ax.set_xlim(97, 107)
                ax.set_ylim(21, 30)

                # 添加网格
                ax.grid(True, alpha=0.3, linestyle='--')

                # 设置等比例显示
                ax.set_aspect('equal', adjustable='box')

            # 调整布局
            fig.tight_layout(pad=2.0)  # 适中的padding

            # 设置画布的充分尺寸
            canvas.setMinimumSize(900, 550)  # 增大最小尺寸
            canvas.setMaximumSize(1200, 550)  # 增大最大尺寸

            self.region_chart_layout.addWidget(canvas)

            # 更新统计信息
            total_coords = len(coordinates)

            # 计算坐标范围
            if coordinates:
                lng_range = f"{min(coord[0] for coord in coordinates):.3f} - {max(coord[0] for coord in coordinates):.3f}"
                lat_range = f"{min(coord[1] for coord in coordinates):.3f} - {max(coord[1] for coord in coordinates):.3f}"

                # 计算工单密度最高的区域
                from collections import Counter
                # 将坐标按0.1度网格分组
                grid_coords = []
                for lng, lat, _ in coordinates:
                    grid_lng = round(lng, 1)
                    grid_lat = round(lat, 1)
                    grid_coords.append(f"{grid_lng},{grid_lat}")

                grid_counter = Counter(grid_coords)
                top_grid = grid_counter.most_common(1)[0] if grid_counter else ("无", 0)

                self.region_stats_label.setText(
                    f"总计: {total_coords} 个工单 | "
                    f"经度范围: {lng_range} | "
                    f"纬度范围: {lat_range} | "
                    f"密度最高区域: {top_grid[0]} ({top_grid[1]} 个工单)"
                )
            else:
                self.region_stats_label.setText("暂无工单数据")

        except Exception as e:
            error_msg = f"生成区域分析图表时出错: {str(e)}"
            print(error_msg)
            QMessageBox.warning(self, "警告", error_msg)

    def create_reason_analysis_chart(self, reason_clusters, empty_count):
        """创建原因分析图表 - 分别创建饼图和柱状图"""
        try:
            # 清除之前的图表
            self.reason_charts = []  # 重置图表列表
            self.current_chart_index = 0

            # 设置中文字体
            import matplotlib
            import matplotlib.font_manager as fm
            import platform

            def setup_chinese_font():
                system = platform.system()
                chinese_fonts = []

                if system == "Windows":
                    default_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
                elif system == "Darwin":  # macOS
                    default_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
                else:  # Linux
                    default_fonts = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans CN']

                available_fonts = [f.name for f in fm.fontManager.ttflist]

                for font_name in default_fonts:
                    if font_name in available_fonts:
                        chinese_fonts.append(font_name)

                for font in fm.fontManager.ttflist:
                    font_name = font.name
                    if any(keyword in font_name.lower() for keyword in
                          ['yahei', 'simhei', 'simsun', 'pingfang', 'heiti', 'songti', 'kaiti', 'noto', 'source han']):
                        if font_name not in chinese_fonts:
                            chinese_fonts.append(font_name)

                return chinese_fonts[:5]

            chinese_fonts = setup_chinese_font()

            try:
                if chinese_fonts:
                    matplotlib.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans', 'Arial']
                else:
                    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']

                matplotlib.rcParams['axes.unicode_minus'] = False
            except Exception as e:
                print(f"设置matplotlib字体时出错: {e}")
                # 使用默认设置
                matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                matplotlib.rcParams['axes.unicode_minus'] = False

            # 创建饼图
            self.create_pie_chart(reason_clusters, empty_count)

            # 创建柱状图
            self.create_bar_chart(reason_clusters, empty_count)

            # 显示第一个图表
            self.update_reason_chart_display()

        except Exception as e:
            error_msg = f"生成原因分析图表时出错: {str(e)}"
            print(error_msg)
            QMessageBox.warning(self, "警告", error_msg)

    def create_pie_chart(self, reason_clusters, empty_count):
        """创建饼图"""
        # 创建饼图 - 减小尺寸确保完整显示
        fig = Figure(figsize=(10, 6), dpi=100)  # 从12x8减小到10x6
        canvas = FigureCanvas(fig)
        canvas.setMaximumHeight(500)  # 从650减小到500
        canvas.setFixedHeight(500)

        # 准备数据
        reasons = list(reason_clusters.keys())
        counts = list(reason_clusters.values())

        # 限制显示的原因数量，避免图表过于拥挤
        max_display = 12
        if len(reasons) > max_display:
            display_reasons = reasons[:max_display-1]
            display_counts = counts[:max_display-1]
            other_count = sum(counts[max_display-1:])
            display_reasons.append("其他")
            display_counts.append(other_count)
        else:
            display_reasons = reasons
            display_counts = counts

        # 生成颜色
        colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe',
                 '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3',
                 '#ff9a9e', '#fecfef', '#ffeaa7', '#74b9ff']
        # 确保有足够的颜色
        while len(colors) < len(display_reasons):
            colors.extend(colors)

        # 创建饼图
        ax = fig.add_subplot(1, 1, 1)

        # 只显示占比较大的标签，避免重叠
        labels = []
        for i, (reason, count) in enumerate(zip(display_reasons, display_counts)):
            percentage = count / sum(display_counts) * 100
            if percentage > 3:  # 只显示占比大于3%的标签
                labels.append(f"{reason}\n({count}次, {percentage:.1f}%)")
            else:
                labels.append("")

        wedges, texts, autotexts = ax.pie(display_counts, labels=labels, colors=colors,
                                          autopct=lambda pct: f'{pct:.1f}%' if pct > 3 else '',
                                          startangle=90, textprops={'fontsize': 9})  # 减小字体

        try:
            ax.set_title('开单原因分布饼图', fontsize=14, fontweight='bold', pad=15)  # 减小标题字体和padding
        except:
            ax.set_title('Reason Distribution Pie Chart', fontsize=14, fontweight='bold', pad=15)

        # 调整布局 - 增加padding确保边缘不被截断
        fig.tight_layout(pad=3.0)

        # 添加到图表列表
        self.reason_charts.append(canvas)

        # 更新统计信息
        total_count = sum(counts)
        total_types = len(reasons)
        top_reason = reasons[0] if reasons else "无"
        top_count = counts[0] if counts else 0

        self.reason_stats_label.setText(
            f"总计: {total_count} 条数据 | "
            f"原因类型: {total_types} 种 | "
            f"主要原因: {top_reason} ({top_count} 次, {top_count/total_count*100:.1f}%) | "
            f"空值: {empty_count} 条"
        )

    def create_bar_chart(self, reason_clusters, empty_count):
        """创建柱状图"""
        # 准备数据
        reasons = list(reason_clusters.keys())
        counts = list(reason_clusters.values())

        # 限制显示的原因数量，避免图表过于拥挤
        max_display = 12
        if len(reasons) > max_display:
            display_reasons = reasons[:max_display-1]
            display_counts = counts[:max_display-1]
            other_count = sum(counts[max_display-1:])
            display_reasons.append("其他")
            display_counts.append(other_count)
        else:
            display_reasons = reasons
            display_counts = counts

        # 生成颜色
        colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe',
                 '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3',
                 '#ff9a9e', '#fecfef', '#ffeaa7', '#74b9ff']
        # 确保有足够的颜色
        while len(colors) < len(display_reasons):
            colors.extend(colors)

        # 创建柱状图 - 减小尺寸确保完整显示
        fig = Figure(figsize=(10, 6), dpi=100)  # 从12x8减小到10x6
        canvas = FigureCanvas(fig)
        canvas.setMaximumHeight(500)  # 从650减小到500
        canvas.setFixedHeight(500)

        ax = fig.add_subplot(1, 1, 1)

        # 截断过长的原因文本
        truncated_reasons = []
        for reason in display_reasons:
            if len(reason) > 15:
                truncated_reasons.append(reason[:12] + "...")
            else:
                truncated_reasons.append(reason)

        bars = ax.bar(range(len(display_counts)), display_counts, color=colors)

        # 设置x轴标签
        ax.set_xticks(range(len(truncated_reasons)))
        ax.set_xticklabels(truncated_reasons, rotation=45, ha='right', fontsize=9)  # 减小字体

        try:
            ax.set_title('开单原因频次柱状图', fontsize=14, fontweight='bold', pad=15)  # 减小标题字体和padding
            ax.set_xlabel('开单原因', fontsize=10)  # 减小字体
            ax.set_ylabel('出现次数', fontsize=10)  # 减小字体
        except:
            ax.set_title('Reason Frequency Bar Chart', fontsize=14, fontweight='bold', pad=15)
            ax.set_xlabel('Reasons', fontsize=10)
            ax.set_ylabel('Frequency', fontsize=10)

        ax.grid(True, alpha=0.3, linestyle='--')

        # 在柱子上显示数值
        for bar, count in zip(bars, display_counts):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(display_counts) * 0.01,
                    str(count), ha='center', va='bottom', fontweight='bold', fontsize=8)  # 减小字体

        # 调整布局 - 增加padding确保边缘不被截断
        fig.tight_layout(pad=3.0)

        # 添加到图表列表
        self.reason_charts.append(canvas)

    def create_archive_duration_chart(self, archive_days, special_cases, invalid_count):
        """创建归档时长分档柱状图"""
        try:
            # 清除之前的图表
            for i in reversed(range(self.archive_chart_layout.count())):
                self.archive_chart_layout.itemAt(i).widget().setParent(None) # type: ignore

            # 创建matplotlib图表 - 更大尺寸充分利用空间
            fig = Figure(figsize=(14, 7), dpi=100)  # 增大图表尺寸
            canvas = FigureCanvas(fig)
            canvas.setMaximumHeight(600)  # 增大最大高度
            canvas.setFixedHeight(600)  # 固定高度

            # 设置中文字体
            import matplotlib
            import matplotlib.font_manager as fm
            import platform

            def setup_chinese_font():
                system = platform.system()
                chinese_fonts = []

                if system == "Windows":
                    default_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
                elif system == "Darwin":  # macOS
                    default_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
                else:  # Linux
                    default_fonts = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans CN']

                available_fonts = [f.name for f in fm.fontManager.ttflist]

                for font_name in default_fonts:
                    if font_name in available_fonts:
                        chinese_fonts.append(font_name)

                for font in fm.fontManager.ttflist:
                    font_name = font.name
                    if any(keyword in font_name.lower() for keyword in
                          ['yahei', 'simhei', 'simsun', 'pingfang', 'heiti', 'songti', 'kaiti', 'noto', 'source han']):
                        if font_name not in chinese_fonts:
                            chinese_fonts.append(font_name)

                return chinese_fonts[:5]

            chinese_fonts = setup_chinese_font()

            try:
                if chinese_fonts:
                    matplotlib.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans', 'Arial']
                else:
                    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']

                matplotlib.rcParams['axes.unicode_minus'] = False
            except Exception as e:
                print(f"设置matplotlib字体时出错: {e}")
                # 使用默认设置
                matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                matplotlib.rcParams['axes.unicode_minus'] = False

            ax = fig.add_subplot(111)

            # 统计超过30天、超过80天、超过100天的数量
            count_over_30 = sum(1 for days in archive_days if days > 30)
            count_over_80 = sum(1 for days in archive_days if days > 80)
            count_over_100 = sum(1 for days in archive_days if days > 100)

            # 准备图表数据
            bin_labels = ["超过30天", "超过80天", "超过100天"]
            bin_counts = [count_over_30, count_over_80, count_over_100]

            print(f"归档时长统计结果:")
            for label, count in zip(bin_labels, bin_counts):
                print(f"  {label}: {count}条")

            # 创建柱状图 - 为3个档次提供颜色
            colors = ['#f5576c', '#764ba2', '#667eea']  # 红色、紫色、蓝色

            bars = ax.bar(bin_labels, bin_counts, color=colors)

            # 设置图表样式
            try:
                ax.set_title('归档时长超期统计 (超过30天/80天/100天)', fontsize=16, fontweight='bold', pad=20)
                ax.set_xlabel('超期档次', fontsize=12)
                ax.set_ylabel('工单数量', fontsize=12)
            except:
                ax.set_title('Archive Duration Overdue Statistics (Over 30/80/100 days)', fontsize=16, fontweight='bold', pad=20)
                ax.set_xlabel('Overdue Category', fontsize=12)
                ax.set_ylabel('Count', fontsize=12)

            ax.grid(True, alpha=0.3, linestyle='--')

            # 在柱子上显示数值
            for bar, count in zip(bars, bin_counts):
                if count > 0:
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(bin_counts) * 0.01,
                           str(count), ha='center', va='bottom', fontweight='bold')

            # 设置y轴范围
            max_count = max(bin_counts) if bin_counts else 0
            if max_count > 0:
                ax.set_ylim(0, max_count * 1.15)
            else:
                ax.set_ylim(0, 1)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            fig.tight_layout()

            self.archive_chart_layout.addWidget(canvas)

            # 更新统计信息
            total_count = len(archive_days)

            self.archive_stats_label.setText(
                f"总计: {total_count} 条 | 超过30天: {count_over_30} 条 | 超过80天: {count_over_80} 条 | 超过100天: {count_over_100} 条"
            )

        except Exception as e:
            error_msg = f"生成归档时长图表时出错: {str(e)}"
            print(error_msg)
            QMessageBox.warning(self, "警告", error_msg)

    def update_month_chart(self, year_str):
        """更新月份图表 - 完全使用真实数据"""
        if not year_str or not hasattr(self, 'year_month_stats'):
            return

        try:
            year = int(year_str)
            month_data = self.year_month_stats[year]

            print(f"正在为{year}年生成图表，月份数据: {dict(month_data)}")

            # 清除之前的图表
            for i in reversed(range(self.chart_layout.count())):
                self.chart_layout.itemAt(i).widget().setParent(None) # type: ignore

            # 创建matplotlib图表 - 更大尺寸充分利用空间
            fig = Figure(figsize=(14, 7), dpi=100)  # 增大图表尺寸
            canvas = FigureCanvas(fig)
            canvas.setMaximumHeight(600)  # 增大最大高度
            canvas.setFixedHeight(600)  # 固定高度

            # 设置中文字体 - 智能检测和配置
            import matplotlib
            import matplotlib.font_manager as fm
            import platform

            def setup_chinese_font():
                """智能设置中文字体"""
                system = platform.system()
                chinese_fonts = []

                # 根据操作系统设置默认中文字体
                if system == "Windows":
                    default_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
                elif system == "Darwin":  # macOS
                    default_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
                else:  # Linux
                    default_fonts = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans CN']

                # 检查系统中可用的中文字体
                available_fonts = [f.name for f in fm.fontManager.ttflist]

                # 查找可用的中文字体
                for font_name in default_fonts:
                    if font_name in available_fonts:
                        chinese_fonts.append(font_name)

                # 额外搜索包含中文字符的字体
                for font in fm.fontManager.ttflist:
                    font_name = font.name
                    if any(keyword in font_name.lower() for keyword in
                          ['yahei', 'simhei', 'simsun', 'pingfang', 'heiti', 'songti', 'kaiti', 'noto', 'source han']):
                        if font_name not in chinese_fonts:
                            chinese_fonts.append(font_name)

                return chinese_fonts[:5]  # 取前5个字体

            chinese_fonts = setup_chinese_font()

            try:
                if chinese_fonts:
                    matplotlib.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans', 'Arial']
                    print(f"配置中文字体: {chinese_fonts}")
                else:
                    # 如果没有找到中文字体，尝试使用系统默认字体
                    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                    print("未找到专用中文字体，使用系统默认字体")

                matplotlib.rcParams['axes.unicode_minus'] = False
            except Exception as e:
                print(f"设置matplotlib字体时出错: {e}")
                # 使用默认设置
                matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                matplotlib.rcParams['axes.unicode_minus'] = False

            # 尝试清除matplotlib字体缓存，确保新设置生效
            try:
                if hasattr(fm, '_rebuild'):
                    fm._rebuild() # type: ignore
                elif hasattr(fm.fontManager, 'findfont'):
                    # 强制重新查找字体
                    fm.fontManager.findfont('sans-serif', rebuild_if_missing=True)
            except:
                pass  # 如果缓存清除失败，继续执行

            ax = fig.add_subplot(111)

            # 准备真实数据 - 使用中文月份标签
            actual_months = []
            actual_counts = []
            month_names = ['1月', '2月', '3月', '4月', '5月', '6月',
                          '7月', '8月', '9月', '10月', '11月', '12月']

            # 获取实际存在数据的月份范围
            if month_data:
                min_month = min(month_data.keys())
                max_month = max(month_data.keys())
                print(f"数据月份范围: {min_month}月 到 {max_month}月")

                # 显示从1月到12月的所有月份
                for month in range(1, 13):
                    actual_months.append(month_names[month-1])
                    actual_counts.append(month_data.get(month, 0))
            else:
                # 如果没有数据，显示全年12个月，都为0
                actual_months = month_names
                actual_counts = [0] * 12
                print("该年份没有数据")

            print(f"图表数据: 月份={actual_months}, 数量={actual_counts}")

            # 创建柱状图 - 根据数据量动态选择颜色
            colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c',
                     '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                     '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3']

            # 为有数据的柱子使用彩色，无数据的使用灰色
            bar_colors = []
            for count in actual_counts:
                if count > 0:
                    bar_colors.append(colors[len(bar_colors) % len(colors)])
                else:
                    bar_colors.append('#e0e0e0')  # 灰色表示无数据

            bars = ax.bar(actual_months, actual_counts, color=bar_colors)

            # 设置图表样式 - 使用中文标题
            try:
                ax.set_title(f'{year}年各月份数据统计', fontsize=16, fontweight='bold', pad=20)
                ax.set_xlabel('月份', fontsize=12)
                ax.set_ylabel('数据行数', fontsize=12)
            except:
                # 如果中文显示失败，回退到英文
                ax.set_title(f'{year} Monthly Data Statistics', fontsize=16, fontweight='bold', pad=20)
                ax.set_xlabel('Month', fontsize=12)
                ax.set_ylabel('Data Count', fontsize=12)

            ax.grid(True, alpha=0.3, linestyle='--')

            # 在柱子上显示数值 - 只在有数据时显示
            for bar, count in zip(bars, actual_counts):
                if count > 0:
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(actual_counts) * 0.01,
                           str(count), ha='center', va='bottom', fontweight='bold')

            # 设置y轴范围 - 根据实际数据调整
            max_count = max(actual_counts) if actual_counts else 0
            if max_count > 0:
                ax.set_ylim(0, max_count * 1.15)
            else:
                ax.set_ylim(0, 1)
                # 如果没有数据，在图表中央显示提示
                try:
                    ax.text(0.5, 0.5, f'{year}年无数据', transform=ax.transAxes,
                           ha='center', va='center', fontsize=14, color='gray')
                except:
                    ax.text(0.5, 0.5, f'No Data for {year}', transform=ax.transAxes,
                           ha='center', va='center', fontsize=14, color='gray')

            # 旋转x轴标签以避免重叠
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            fig.tight_layout()

            self.chart_layout.addWidget(canvas)

            # 更新统计信息 - 使用真实数据，界面保持中文
            total_count = sum(actual_counts)
            if total_count > 0:
                max_count_value = max(actual_counts)
                max_month_index = actual_counts.index(max_count_value)
                max_month_name = actual_months[max_month_index]

                # 计算有数据的月份数
                months_with_data = sum(1 for count in actual_counts if count > 0)

                self.stats_label.setText(
                    f"总计: {total_count} 条 | 峰值: {max_month_name} ({max_count_value} 条) | 覆盖: {months_with_data}/12 月"
                )
            else:
                self.stats_label.setText(f"{year}年: 暂无数据")

        except Exception as e:
            error_msg = f"生成图表时出错: {str(e)}"
            print(error_msg)
            QMessageBox.warning(self, "警告", error_msg)

    def find_archive_status_column(self, data):
        """自动查找'是否归档'列的位置"""
        if not data or len(data) < 2:
            return None

        # 在前两行中查找包含"归档"关键字的列
        archive_keywords = ['是否归档', '归档状态', '归档', 'archive', 'archived']

        for row_idx in range(min(2, len(data))):  # 检查前两行
            row = data[row_idx]
            for col_idx, cell in enumerate(row):
                if cell and isinstance(cell, str):
                    cell_lower = cell.lower().strip()
                    for keyword in archive_keywords:
                        if keyword in cell_lower:
                            print(f"找到归档状态列: 第{row_idx+1}行第{col_idx+1}列 '{cell}'")
                            return col_idx

        return None

    def filter_archived_data(self, data):
        """过滤已归档的数据"""
        if not data or len(data) < 3:
            return data

        # 使用O列（索引为14）作为归档状态列
        archive_status_column = 14  # O列的索引

        # 过滤已归档的数据
        filtered_data = [data[0], data[1]]  # 保留前两行表头
        archived_count = 0
        total_count = 0

        for row in data[2:]:  # 从第3行开始处理数据
            total_count += 1
            if len(row) > archive_status_column:
                archive_status = str(row[archive_status_column]).strip() if row[archive_status_column] else ""
                # 检查是否为已归档状态（可能的值：已归档、归档、1、是等）
                if archive_status.lower() in ['已归档', '归档', '1', '是', 'yes', 'true', '✓']:
                    filtered_data.append(row)
                    archived_count += 1

        print(f"数据过滤完成: 总数据{total_count}行，已归档{archived_count}行")

        if archived_count == 0:
            print("警告: 没有找到任何已归档的数据")

        return filtered_data

    def perform_analysis(self, analysis_type):
        """执行分析功能"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "请先导入Excel文件")
            return

        # 过滤已归档的数据
        archived_data = self.filter_archived_data(self.current_data)

        if len(archived_data) <= 2:  # 只有表头，没有数据
            QMessageBox.warning(self, "警告", "没有找到已归档的数据，无法进行分析")
            return

        # 临时保存原数据，使用过滤后的数据进行分析
        original_data = self.current_data
        self.current_data = archived_data

        try:
            if analysis_type == "月份变化":
                self.show_month_analysis_page()
            elif analysis_type == "归档时长":
                self.show_archive_analysis_page()
            elif analysis_type == "原因分析":
                self.show_reason_analysis_page()
            elif analysis_type == "区域分析":
                self.show_region_analysis_page()
            # 移除了其他分析类型的弹窗处理
        finally:
            # 恢复原数据
            self.current_data = original_data

    def select_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls)"
        )
        if file_path:
            self.load_excel_file(file_path)



    def load_excel_file(self, file_path):
        self.status_label.setText("正在加载...")
        self.status_label.setStyleSheet("color: #ffc107; font-weight: bold;")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动后台线程加载文件
        self.worker_thread = ExcelReaderThread(file_path)
        self.worker_thread.progress.connect(self.progress_bar.setValue)
        self.worker_thread.finished.connect(self.on_file_loaded)
        self.worker_thread.error.connect(self.on_load_error)
        self.worker_thread.start()

        self.last_file_path = file_path

    def on_file_loaded(self, data):
        try:
            # 保存到数据库
            file_name = os.path.basename(self.last_file_path) if self.last_file_path else "未知文件"
            file_size = os.path.getsize(self.last_file_path) if self.last_file_path and os.path.exists(self.last_file_path) else 0

            # 创建上传记录
            upload_id = self.data_manager.create_upload_record(
                file_name=file_name,
                file_path=self.last_file_path,
                file_size=file_size,
                description=f"通过界面上传于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            if upload_id:
                # 保存工单数据
                valid_count = self.data_manager.insert_work_orders(upload_id, data)

                # 重新加载所有数据（包括新上传的）
                self.load_existing_data()

                # 重置测试时间数据缓存，确保下次访问时重新加载
                self.reset_test_time_cache()

                self.status_label.setText(f"加载并保存完成 (有效数据: {valid_count})")
                self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")

                # 显示成功消息
                QMessageBox.information(
                    self, "成功",
                    f"成功加载并保存Excel文件！\n\n"
                    f"文件: {file_name}\n"
                    f"总行数: {len(data)}\n"
                    f"有效数据: {valid_count} 行\n"
                    f"数据已保存到数据库，可以使用分析功能了！"
                )
            else:
                # 如果数据库保存失败，仍然可以使用当前数据
                self.current_data = data
                self.status_label.setText("加载完成 (未保存到数据库)")
                self.status_label.setStyleSheet("color: #ffc107; font-weight: bold;")

                QMessageBox.warning(
                    self, "警告",
                    f"Excel文件加载成功，但保存到数据库失败！\n\n"
                    f"当前会话仍可使用数据，但数据不会持久化保存。"
                )

        except Exception as e:
            # 如果出错，回退到原来的逻辑
            self.current_data = data
            self.status_label.setText("加载完成 (数据库操作失败)")
            self.status_label.setStyleSheet("color: #ffc107; font-weight: bold;")

            QMessageBox.warning(
                self, "警告",
                f"Excel文件加载成功，但数据库操作出错：\n{str(e)}\n\n"
                f"当前会话仍可使用数据。"
            )

        finally:
            self.progress_bar.setVisible(False)

            # 显示数据
            if self.current_data:
                self.display_data(self.current_data)

            # 启用分析按钮
            self.set_analysis_buttons_enabled(True)
            if hasattr(self, 'archive_analysis_buttons'):
                self.set_archive_analysis_buttons_enabled(True)

    def on_load_error(self, error_msg):
        self.status_label.setText("加载失败")
        self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        self.progress_bar.setVisible(False)

        QMessageBox.critical(self, "错误", f"加载文件时出错：\n{error_msg}")

    def display_data(self, data):
        if not data:
            return

        self.data_group.setVisible(True)

        # 设置表格
        self.data_table.setRowCount(min(len(data), 100))  # 最多显示100行
        self.data_table.setColumnCount(len(data[0]) if data[0] else 0)

        # 设置表头
        if data:
            headers = [str(cell) if cell is not None else "" for cell in data[0]]
            self.data_table.setHorizontalHeaderLabels(headers)

            # 填充数据（跳过表头行）
            for row_idx, row_data in enumerate(data[1:101]):  # 最多显示100行数据
                for col_idx, cell_value in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_value) if cell_value is not None else "")
                    self.data_table.setItem(row_idx, col_idx, item)

        # 调整列宽
        self.data_table.resizeColumnsToContents()

    def create_process_pages(self):
        """创建所有流程处理页面"""
        process_configs = {
            'planning': {
                'title': '📊 规划流程处理',
                'color': '#667eea',
                'items': [
                    {'key': 'big_network', 'label': '大网', 'column': 'planning_big_network'},
                    {'key': 'non_big_network', 'label': '非大网', 'column': 'planning_non_big_network'}
                ]
            },
            'construction': {
                'title': '🏗️ 建设流程处理',
                'color': '#28a745',
                'items': [
                    {'key': 'property', 'label': '物业', 'column': 'construction_property'},
                    {'key': 'transmission', 'label': '传输', 'column': 'construction_transmission'},
                    {'key': 'power', 'label': '动力', 'column': 'construction_power'},
                    {'key': 'tower', 'label': '杆塔', 'column': 'construction_tower'},
                    {'key': 'equipment', 'label': '设备安装', 'column': 'construction_equipment'}
                ]
            },
            'maintenance': {
                'title': '🔧 维护流程处理',
                'color': '#ffc107',
                'items': [
                    {'key': 'sporadic', 'label': '零星', 'column': 'maintenance_sporadic'},
                    {'key': 'outsourced', 'label': '代维', 'column': 'maintenance_outsourced'}
                ]
            },
            'optimization': {
                'title': '⚡ 优化流程处理',
                'color': '#17a2b8',
                'items': [
                    {'key': 'antenna', 'label': '天线调整', 'column': 'optimization_antenna'},
                    {'key': 'backend', 'label': '后台', 'column': 'optimization_backend'}
                ]
            },
            'customer': {
                'title': '👥 客户流程处理',
                'color': '#dc3545',
                'items': [
                    {'key': 'field_test', 'label': '现场测试', 'column': 'customer_field_test'},
                    {'key': 'communication', 'label': '客户沟通', 'column': 'customer_communication'}
                ]
            }
        }

        for process_key, config in process_configs.items():
            page = self.create_single_process_page(process_key, config)
            self.process_pages[process_key] = page

    def create_single_process_page(self, process_key, config):
        """创建单个流程处理页面"""
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题和返回按钮
        header_layout = QHBoxLayout()

        back_btn = ModernButton("← 返回详细操作")
        back_btn.clicked.connect(self.show_transit_detail_page_from_process)
        back_btn.setFixedWidth(120)
        back_btn.setFixedHeight(40)
        header_layout.addWidget(back_btn)

        header_layout.addStretch()

        title = QLabel(config['title'])
        title.setFont(QFont("Microsoft YaHei", 22, QFont.Weight.Bold))
        title.setStyleSheet("color: #2c3e50;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 流程信息卡片 - 根据时间状态设置颜色
        info_frame = QFrame()
        info_frame.setFixedHeight(120)
        info_frame.setObjectName(f"info_frame_{process_key}")  # 设置对象名称以便动态更新

        # 获取时间状态和对应的颜色（初始状态，后续会动态更新）
        try:
            time_status_info = self.get_process_time_status_info(process_key)
            background_color = time_status_info['color']
        except:
            # 如果获取失败，使用默认颜色
            background_color = 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #667eea, stop:1 #764ba2)'
            time_status_info = {'text': '⏱️ 时间状态：等待流程选择'}

        info_frame.setStyleSheet(f"""
            QFrame {{
                background: {background_color};
                border-radius: 15px;
                border: none;
            }}
        """)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(25, 15, 25, 15)
        info_layout.setSpacing(8)

        # 流程标题
        process_title_label = QLabel(f"当前流程: {config['title']}")
        process_title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        process_title_label.setStyleSheet("color: white;")
        info_layout.addWidget(process_title_label)

        # 流程说明
        process_info_label = QLabel("请为以下环节选择状态（是/否）")
        process_info_label.setFont(QFont("Microsoft YaHei", 11))
        process_info_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        info_layout.addWidget(process_info_label)

        # 时间状态显示
        time_status_text = time_status_info['text']
        time_status_label = QLabel(time_status_text)
        time_status_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        time_status_label.setStyleSheet("color: rgba(255, 255, 255, 0.95);")
        time_status_label.setObjectName(f"time_status_label_{process_key}")  # 设置对象名称以便动态更新
        info_layout.addWidget(time_status_label)

        layout.addWidget(info_frame)

        # 环节选择区域 - 根据时间状态设置背景颜色
        items_frame = QFrame()
        items_frame.setObjectName(f"items_frame_{process_key}")  # 设置对象名称以便动态更新

        # 根据时间状态设置环节选择区域的背景颜色（初始状态，后续会动态更新）
        try:
            if 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:1 #20c997)' in background_color:
                # 正常状态 - 浅绿色背景
                items_background = "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(40, 167, 69, 0.1), stop:1 rgba(32, 201, 151, 0.05));"
                border_color = "border: 2px solid rgba(40, 167, 69, 0.3);"
            elif 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)' in background_color:
                # 超时状态 - 浅红色背景
                items_background = "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(220, 53, 69, 0.1), stop:1 rgba(200, 35, 51, 0.05));"
                border_color = "border: 2px solid rgba(220, 53, 69, 0.3);"
            else:
                # 默认状态 - 原来的背景
                items_background = "background: #f8f9fa;"
                border_color = "border: 1px solid #e9ecef;"
        except:
            # 如果出错，使用默认背景
            items_background = "background: #f8f9fa;"
            border_color = "border: 1px solid #e9ecef;"

        items_frame.setStyleSheet(f"""
            QFrame {{
                {items_background}
                {border_color}
                border-radius: 15px;
            }}
        """)

        items_layout = QVBoxLayout(items_frame)
        items_layout.setContentsMargins(25, 20, 25, 20)
        items_layout.setSpacing(20)

        # 环节选择标题
        items_title = QLabel("🔧 环节状态设置")
        items_title.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        items_title.setStyleSheet("color: #495057;")
        items_layout.addWidget(items_title)

        # 创建环节选择控件
        items_container = QWidget()
        items_container_layout = QGridLayout(items_container)
        items_container_layout.setSpacing(15)

        # 存储当前页面的选择控件
        page_selections = {}

        for i, item in enumerate(config['items']):
            # 环节标签 - 根据时间状态调整颜色
            item_label = QLabel(f"📋 {item['label']}")
            item_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))

            # 根据时间状态设置标签颜色
            if 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:1 #20c997)' in background_color:
                label_color = "#28a745"  # 绿色
            elif 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)' in background_color:
                label_color = "#dc3545"  # 红色
            else:
                label_color = "#495057"  # 默认颜色

            item_label.setStyleSheet(f"color: {label_color}; font-weight: bold;")
            items_container_layout.addWidget(item_label, i, 0)

            # 状态选择按钮组
            button_group = QButtonGroup()
            button_group.setProperty("item_key", item['key'])  # 添加标识

            # 根据时间状态设置按钮颜色
            if 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:1 #20c997)' in background_color:
                button_color = "#28a745"  # 绿色
            elif 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)' in background_color:
                button_color = "#dc3545"  # 红色
            else:
                button_color = config['color']  # 默认颜色

            yes_radio = QRadioButton("是")
            yes_radio.setFont(QFont("Microsoft YaHei", 11))
            yes_radio.setStyleSheet(f"""
                QRadioButton {{
                    color: #495057;
                    spacing: 8px;
                }}
                QRadioButton::indicator {{
                    width: 16px;
                    height: 16px;
                    border: 2px solid {button_color};
                    border-radius: 8px;
                    background: white;
                }}
                QRadioButton::indicator:checked {{
                    background: {button_color};
                    border: 2px solid {button_color};
                }}
            """)

            no_radio = QRadioButton("否")
            no_radio.setFont(QFont("Microsoft YaHei", 11))

            # "否"按钮始终使用灰色，但在超时状态下使用更深的红灰色
            if 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)' in background_color:
                no_button_color = "#8b2635"  # 深红灰色
            else:
                no_button_color = "#6c757d"  # 默认灰色

            no_radio.setStyleSheet(f"""
                QRadioButton {{
                    color: #495057;
                    spacing: 8px;
                }}
                QRadioButton::indicator {{
                    width: 16px;
                    height: 16px;
                    border: 2px solid {no_button_color};
                    border-radius: 8px;
                    background: white;
                }}
                QRadioButton::indicator:checked {{
                    background: {no_button_color};
                    border: 2px solid {no_button_color};
                }}
            """)

            button_group.addButton(yes_radio, 1)  # 是 = 1
            button_group.addButton(no_radio, 0)   # 否 = 0

            # 创建按钮容器
            button_container = QWidget()
            button_layout = QHBoxLayout(button_container)
            button_layout.setContentsMargins(0, 0, 0, 0)
            button_layout.setSpacing(20)
            button_layout.addWidget(yes_radio)
            button_layout.addWidget(no_radio)
            button_layout.addStretch()

            items_container_layout.addWidget(button_container, i, 1)

            # 保存选择控件
            page_selections[item['key']] = {
                'group': button_group,
                'column': item['column'],
                'label': item['label'],
                'yes_radio': yes_radio,
                'no_radio': no_radio
            }

            # 将按钮组保存到页面对象的属性中，方便后续查找
            setattr(page, f'button_group_{item["key"]}', button_group)

        items_layout.addWidget(items_container)

        # 现在所有按钮都创建完成了，连接信号
        for item_key, selection_info in page_selections.items():
            button_group = selection_info['group']
            # 连接信号，当选择改变时保存状态
            # 使用默认参数来避免闭包问题
            def create_save_handler(pk, ps, ik):
                def handler(checked):
                    print(f"🔘 按钮点击事件触发: {pk}.{ik}")
                    self.save_current_page_state(pk, ps)
                return handler

            button_group.buttonClicked.connect(create_save_handler(process_key, page_selections, item_key))

        # 操作按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        button_layout.addStretch()

        # 完成当前流程按钮 - 根据时间状态设置样式
        complete_btn = ModernButton("✅ 完成当前流程", True)
        complete_btn.setFixedWidth(150)
        complete_btn.setFixedHeight(40)
        complete_btn.clicked.connect(lambda: self.complete_current_process(process_key, page_selections))

        # 根据时间状态设置按钮样式
        if 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:1 #20c997)' in background_color:
            # 正常状态 - 绿色按钮
            complete_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #28a745, stop:1 #20c997);
                    color: white;
                    border: none;
                    border-radius: 20px;
                    font-weight: bold;
                    padding: 0 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #218838, stop:1 #1e7e34);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1e7e34, stop:1 #155724);
                }
            """)
        elif 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)' in background_color:
            # 超时状态 - 红色按钮
            complete_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #dc3545, stop:1 #c82333);
                    color: white;
                    border: none;
                    border-radius: 20px;
                    font-weight: bold;
                    padding: 0 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #c82333, stop:1 #bd2130);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #bd2130, stop:1 #b21f2d);
                }
            """)
        # 如果是默认状态，保持原有的ModernButton样式

        button_layout.addWidget(complete_btn)

        items_layout.addLayout(button_layout)
        layout.addWidget(items_frame, 1)

        return page

    def start_process_handling(self):
        """开始流程处理"""
        if not self.selected_processes:
            return

        # 初始化流程结果存储
        if not hasattr(self, 'process_results'):
            self.process_results = {}

        # 处理规划流程的特殊逻辑
        if '规划' in self.selected_processes:
            self.handle_planning_process()
        else:
            # 显示第一个流程页面
            self.show_current_process_page()

    def show_current_process_page(self):
        """显示当前流程页面"""
        if self.current_process_index >= len(self.selected_processes):
            # 所有流程完成
            self.complete_all_processes()
            return

        current_process = self.selected_processes[self.current_process_index]
        process_key_map = {
            '规划': 'planning',
            '建设': 'construction',
            '维护': 'maintenance',
            '优化': 'optimization',
            '客户': 'customer'
        }

        process_key = process_key_map.get(current_process)
        if process_key and process_key in self.process_pages:
            # 在显示页面前，动态更新时间状态
            self.update_process_page_time_status(process_key)

            self.stacked_widget.setCurrentWidget(self.process_pages[process_key])

            # 使用QTimer延迟恢复状态，确保页面完全加载后再恢复
            from PyQt6.QtCore import QTimer
            def delayed_restore():
                # 恢复页面选择状态
                if hasattr(self, 'pending_page_selections'):
                    self.apply_page_selections(process_key, self.pending_page_selections)
                    delattr(self, 'pending_page_selections')
                else:
                    # 检查工单是否真正处于流程处理状态
                    # 只有当前工单有进行中的流程进度时，才恢复选择状态
                    # 这样可以避免未开始工单被错误地预填之前的处理结果
                    if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
                        # 检查当前工单是否有进行中的流程进度
                        progress = self.data_manager.get_process_progress(self.current_work_order_id)
                        if progress and progress.get('status') == 'in_progress' and progress.get('work_order_id') == self.current_work_order_id:
                            # 只有当前工单的进行中流程才恢复选择状态
                            db_selections = self.load_page_selections_from_database(process_key)
                            if db_selections:
                                print(f"🔄 从数据库恢复当前工单 {self.current_work_order_id} 的 {process_key} 流程选择状态: {db_selections}")
                                self.apply_page_selections(process_key, db_selections)
                            else:
                                print(f"✅ 当前工单 {self.current_work_order_id} 的 {process_key} 流程无已保存状态，保持空白")
                        else:
                            print(f"✅ 当前工单 {self.current_work_order_id} 无进行中流程，{process_key} 页面保持空白状态")
                            # 确保清理页面状态，防止显示其他工单的状态
                            self.clear_current_page_selections(process_key)

            # 延迟100毫秒执行恢复操作
            QTimer.singleShot(100, delayed_restore)

    def apply_page_selections(self, process_key, page_selections):
        """应用页面选择状态"""
        try:
            if not page_selections:
                print(f"⚠️ {process_key} 没有选择状态需要恢复")
                return

            print(f"🔄 开始恢复 {process_key} 页面选择状态: {page_selections}")

            # 获取当前页面
            page = self.process_pages.get(process_key)
            if not page:
                print(f"❌ 找不到 {process_key} 页面")
                return

            # 根据保存的选择状态恢复单选按钮
            for item_key, selection_value in page_selections.items():
                print(f"🔧 尝试恢复 {item_key} = {selection_value}")

                # 尝试通过属性名直接获取按钮组
                button_group = getattr(page, f'button_group_{item_key}', None)

                if button_group:
                    print(f"✅ 找到匹配的按钮组: {item_key}")

                    # 根据选择值设置按钮状态
                    if selection_value == "是":
                        # 选择"是"按钮（ID通常为1）
                        yes_button = button_group.button(1)
                        if yes_button:
                            yes_button.setChecked(True)
                            print(f"✅ 设置 {item_key} 为 '是'")
                        else:
                            print(f"❌ 找不到 {item_key} 的 '是' 按钮")
                    elif selection_value == "否":
                        # 选择"否"按钮（ID通常为0）
                        no_button = button_group.button(0)
                        if no_button:
                            no_button.setChecked(True)
                            print(f"✅ 设置 {item_key} 为 '否'")
                        else:
                            print(f"❌ 找不到 {item_key} 的 '否' 按钮")
                else:
                    print(f"❌ 找不到 {item_key} 对应的按钮组")

                    # 备用方法：使用findChildren查找
                    print(f"🔍 尝试备用查找方法...")
                    button_groups = page.findChildren(QButtonGroup)
                    print(f"🔍 找到 {len(button_groups)} 个按钮组")

                    for group in button_groups:
                        group_item_key = group.property("item_key")
                        print(f"  - 按钮组 item_key: {group_item_key}")
                        if group_item_key == item_key:
                            print(f"✅ 通过备用方法找到匹配的按钮组: {item_key}")

                            # 根据选择值设置按钮状态
                            if selection_value == "是":
                                yes_button = group.button(1)
                                if yes_button:
                                    yes_button.setChecked(True)
                                    print(f"✅ 设置 {item_key} 为 '是'")
                            elif selection_value == "否":
                                no_button = group.button(0)
                                if no_button:
                                    no_button.setChecked(True)
                                    print(f"✅ 设置 {item_key} 为 '否'")
                            break

            print(f"🎉 {process_key} 页面选择状态恢复完成")

        except Exception as e:
            print(f"应用页面选择状态失败: {e}")
            import traceback
            traceback.print_exc()

    def save_current_page_state(self, process_key, page_selections):
        """保存当前页面的选择状态到数据库对应列"""
        try:
            print(f"保存页面状态: {process_key}")

            if not hasattr(self, 'current_work_order_id') or not self.current_work_order_id:
                print("没有当前工单ID，跳过保存")
                return

            # 定义选择项到数据库列的映射关系
            column_mapping = {
                # 规划流程 (在原因选择时处理)
                'planning_big_network': 'planning_big_network',
                'planning_non_big_network': 'planning_non_big_network',

                # 建设流程
                'construction_property': 'construction_property',
                'construction_transmission': 'construction_transmission',
                'construction_power': 'construction_power',
                'construction_tower': 'construction_tower',
                'construction_equipment': 'construction_equipment',

                # 维护流程
                'maintenance_sporadic': 'maintenance_sporadic',
                'maintenance_outsourced': 'maintenance_outsourced',

                # 优化流程
                'optimization_antenna': 'optimization_antenna',
                'optimization_backend': 'optimization_backend',

                # 客户流程
                'customer_field_test': 'customer_field_test',
                'customer_communication': 'customer_communication'
            }

            # 获取当前页面的选择状态并直接更新数据库
            updates = {}

            for item_key, selection_info in page_selections.items():
                button_group = selection_info['group']
                checked_button = button_group.checkedButton()

                if checked_button is not None:
                    # 获取选择的值（1=是，0=否）
                    selection_value = "是" if button_group.checkedId() == 1 else "否"

                    # 构建完整的映射键
                    full_key = f"{process_key}_{item_key}"

                    # 如果有对应的数据库列，准备更新
                    if full_key in column_mapping:
                        column_name = column_mapping[full_key]
                        updates[column_name] = selection_value
                        print(f"准备更新 {full_key} -> {column_name} = {selection_value}")
                    else:
                        print(f"映射键 {full_key} 不在映射表中")

            # 如果有需要更新的列，立即更新数据库
            if updates:
                success = self.update_work_order_columns(self.current_work_order_id, updates)
                if success:
                    print(f"✅ 成功保存选择状态到数据库: {updates}")
                else:
                    print(f"❌ 保存选择状态到数据库失败")

            # 同时保存到流程进度表（用于恢复流程状态）
            if not hasattr(self, 'current_page_selections'):
                self.current_page_selections = {}

            current_selections = {}
            for item_key, selection_info in page_selections.items():
                button_group = selection_info['group']
                checked_button = button_group.checkedButton()
                if checked_button is not None:
                    selection_value = "是" if button_group.checkedId() == 1 else "否"
                    current_selections[item_key] = selection_value

            self.current_page_selections[process_key] = current_selections

            if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
                progress_success = self.data_manager.update_process_progress(
                    self.current_work_order_id,
                    self.current_process_index,
                    getattr(self, 'process_results', {}),
                    self.current_page_selections
                )
                if progress_success:
                    print(f"✅ 成功更新流程进度")
                else:
                    print(f"❌ 更新流程进度失败")

        except Exception as e:
            print(f"保存当前页面状态失败: {e}")

    def update_work_order_columns(self, work_order_id, updates):
        """更新工单数据库的指定列"""
        try:
            if not updates:
                return True

            # 构建SQL更新语句
            set_clauses = []
            values = []

            for column_name, value in updates.items():
                set_clauses.append(f"{column_name} = ?")
                values.append(value)

            # 添加更新时间
            set_clauses.append("updated_at = CURRENT_TIMESTAMP")
            values.append(work_order_id)

            sql = f"UPDATE work_orders SET {', '.join(set_clauses)} WHERE id = ?"

            # 执行更新
            conn = self.data_manager.db.get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, values)
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            print(f"成功更新工单 {work_order_id} 的列: {list(updates.keys())}，影响行数: {affected_rows}")
            return affected_rows > 0

        except Exception as e:
            print(f"更新工单列失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_process_page_time_status(self, process_key):
        """动态更新流程页面的时间状态"""
        try:
            if process_key not in self.process_pages:
                return

            page = self.process_pages[process_key]

            # 获取时间状态信息
            time_status_info = self.get_process_time_status_info(process_key)
            background_color = time_status_info['color']
            time_status_text = time_status_info['text']

            # 查找页面中的信息框架和时间状态标签
            for child in page.findChildren(QFrame):
                if child.objectName() == f"info_frame_{process_key}":
                    # 更新信息框架的背景颜色
                    child.setStyleSheet(f"""
                        QFrame {{
                            background: {background_color};
                            border-radius: 15px;
                            border: none;
                        }}
                    """)
                    break

            # 查找并更新时间状态标签
            for child in page.findChildren(QLabel):
                if child.objectName() == f"time_status_label_{process_key}":
                    child.setText(time_status_text)
                    break

            # 查找并更新环节选择区域的样式
            for child in page.findChildren(QFrame):
                if child.objectName() == f"items_frame_{process_key}":
                    # 根据时间状态设置环节选择区域的背景颜色
                    if 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:1 #20c997)' in background_color:
                        # 正常状态 - 浅绿色背景
                        items_background = "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(40, 167, 69, 0.1), stop:1 rgba(32, 201, 151, 0.05));"
                        border_color = "border: 2px solid rgba(40, 167, 69, 0.3);"
                    elif 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)' in background_color:
                        # 超时状态 - 浅红色背景
                        items_background = "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(220, 53, 69, 0.1), stop:1 rgba(200, 35, 51, 0.05));"
                        border_color = "border: 2px solid rgba(220, 53, 69, 0.3);"
                    else:
                        # 默认状态 - 原来的背景
                        items_background = "background: #f8f9fa;"
                        border_color = "border: 1px solid #e9ecef;"

                    child.setStyleSheet(f"""
                        QFrame {{
                            {items_background}
                            {border_color}
                            border-radius: 15px;
                        }}
                    """)
                    break

        except Exception as e:
            print(f"更新流程页面时间状态时出错: {e}")

    def show_transit_detail_page_from_process(self):
        """从流程页面返回到详细操作页面"""
        self.stacked_widget.setCurrentWidget(self.transit_detail_page)

    def handle_planning_process(self):
        """处理规划流程的特殊逻辑"""
        # 规划流程直接使用选择的大网/非大网
        planning_choice = None

        # 从选择的流程详情中获取规划选择
        for detail in self.selected_process_details:
            if detail.startswith('规划:'):
                if '大网' in detail:
                    planning_choice = '大网'
                elif '非大网' in detail:
                    planning_choice = '非大网'
                break

        if planning_choice:
            # 直接更新数据库对应列
            updates = {}
            if planning_choice == '大网':
                updates['planning_big_network'] = '是'  # R列：规划-大网
                updates['planning_non_big_network'] = '否'  # S列：规划-非大网
            elif planning_choice == '非大网':
                updates['planning_big_network'] = '否'  # R列：规划-大网
                updates['planning_non_big_network'] = '是'  # S列：规划-非大网

            # 立即更新数据库
            if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
                self.update_work_order_columns(self.current_work_order_id, updates)
                print(f"规划流程：选择了{planning_choice}，已更新数据库")

            # 保存规划流程结果（用于流程状态管理）
            self.process_results['planning'] = {
                'planning_big_network': '是' if planning_choice == '大网' else '否',
                'planning_non_big_network': '是' if planning_choice == '非大网' else '否'
            }

            # 跳过规划流程，进入下一个流程
            self.current_process_index += 1
            self.show_current_process_page()
        else:
            # 如果没有找到规划选择，显示规划流程页面让用户选择
            self.show_current_process_page()

    def complete_current_process(self, process_key, page_selections):
        """完成当前流程"""
        # 检查是否所有环节都已选择
        selected_values = {}
        missing_selections = []
        no_selections = []  # 记录选择"否"的环节

        for item_key, selection_info in page_selections.items():
            button_group = selection_info['group']
            checked_button = button_group.checkedButton()

            if checked_button is None:
                missing_selections.append(selection_info.get('label', item_key))
            else:
                # 获取选择的值（1=是，0=否）
                selection_value = "是" if button_group.checkedId() == 1 else "否"
                selected_values[selection_info['column']] = selection_value

                # 如果选择了"否"，记录下来
                if selection_value == "否":
                    no_selections.append(selection_info.get('label', item_key))

        if missing_selections:
            QMessageBox.warning(self, "提示", f"请为以下环节选择状态：\n{', '.join(missing_selections)}")
            return

        # 检查是否所有环节都选择了"是"
        if no_selections:
            QMessageBox.warning(self, "提示",
                               f"只有当所有环节都选择'是'时才能完成当前流程。\n\n"
                               f"以下环节选择了'否'：\n{', '.join(no_selections)}\n\n"
                               f"请将所有环节都设置为'是'后再完成流程。")
            return

        # 保存当前流程的选择结果
        if not hasattr(self, 'process_results'):
            self.process_results = {}
        self.process_results[process_key] = selected_values

        # 进入下一个流程
        self.current_process_index += 1

        # 更新数据库中的流程进度
        if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
            # 清除当前页面状态，因为已经完成了
            if hasattr(self, 'current_page_selections'):
                delattr(self, 'current_page_selections')

            self.data_manager.update_process_progress(
                self.current_work_order_id,
                self.current_process_index,
                self.process_results,
                None  # 当前页面状态为空，因为已完成
            )

        self.show_current_process_page()

    def complete_all_processes(self):
        """完成所有流程"""
        # 保存流程结果到数据库
        self.save_process_results_to_database()

        # 更新Q列为已归档
        self.update_archive_status()

        # 标记流程进度为已完成
        if hasattr(self, 'current_work_order_id') and self.current_work_order_id:
            self.data_manager.complete_process_progress(self.current_work_order_id)

        # 显示完成信息
        QMessageBox.information(self, "流程完成",
                               f"所有流程已完成！\n\n已处理流程：{' → '.join(self.selected_processes)}\n\n工单已标记为已归档。")

        # 返回到在途数据处理页面并刷新数据
        self.show_transit_data_page()
        if self.current_data:
            self.update_transit_stats()
            self.refresh_transit_preview_data()

    def save_process_results_to_database(self):
        """保存流程结果到数据库"""
        if not hasattr(self, 'process_results') or not self.process_results:
            return

        try:
            # 暂时打印结果，后续可以实现具体的数据库更新逻辑
            print("流程处理结果：")
            for process_key, results in self.process_results.items():
                print(f"{process_key}: {results}")

        except Exception as e:
            print(f"保存流程结果时出错: {e}")
            QMessageBox.warning(self, "错误", f"保存流程结果时出错: {e}")

    def update_archive_status(self):
        """更新当前工单的归档状态"""
        if not hasattr(self, 'current_work_order_data') or not self.current_work_order_data:
            return

        try:
            # 获取当前工单的行号
            row_number = int(self.current_work_order_data[0])  # 第一列是行号

            # 在内存中的数据更新Q列（索引16）
            if self.current_data and len(self.current_data) > row_number - 1:
                # row_number是从3开始的（Excel行号），需要转换为数组索引
                data_index = row_number - 1  # 转换为0基索引
                if data_index < len(self.current_data):
                    # 确保行数据有足够的列
                    while len(self.current_data[data_index]) <= 16:
                        self.current_data[data_index].append("")

                    # 更新Q列为已归档
                    self.current_data[data_index][16] = "已归档"
                    print(f"已将第{row_number}行的归档状态更新为：已归档")

                    # 保存已归档工单信息到数据库
                    self.save_archived_work_order_info(row_number)

        except Exception as e:
            print(f"更新归档状态时出错: {e}")
            QMessageBox.warning(self, "错误", f"更新归档状态时出错: {e}")

    def save_archived_work_order_info(self, row_number):
        """保存已归档工单信息到数据库"""
        try:
            # 获取工单ID
            work_order_id = self.get_work_order_id_by_row(row_number)
            if not work_order_id:
                print(f"无法获取第{row_number}行的工单ID")
                return

            # 获取工单号（B列）
            data_index = row_number - 1
            if (self.current_data and 0 <= data_index < len(self.current_data)
                and len(self.current_data[data_index]) > 1):
                work_order_number = str(self.current_data[data_index][1])  # B列索引为1

                # 保存到数据库
                success = self.data_manager.save_archived_work_order(
                    work_order_id, work_order_number, row_number
                )

                if success:
                    print(f"已保存归档工单信息: 工单号={work_order_number}, 行号={row_number}")
                else:
                    print(f"保存归档工单信息失败")
            else:
                print(f"无法获取第{row_number}行的工单号")

        except Exception as e:
            print(f"保存归档工单信息时出错: {e}")

    def show_archived_management_page(self):
        """显示归档工单管理页面"""
        # 更新按钮状态
        self.update_sidebar_buttons(self.archived_mgmt_btn)

        # 切换到归档工单管理页面
        self.stacked_widget.setCurrentWidget(self.archived_management_page)

        # 自动刷新归档数据
        self.refresh_archived_data()

    def refresh_archived_data(self):
        """刷新归档工单数据"""
        try:
            if not hasattr(self, 'data_manager') or not self.data_manager:
                self.archived_stats_label.setText("请先导入数据")
                self.archived_table.setRowCount(0)
                return

            print("🔄 开始加载归档工单数据...")

            # 从数据库获取归档工单数据
            archived_orders = self.data_manager.get_archived_work_orders()

            # 更新统计信息
            total_count = len(archived_orders)
            self.archived_stats_label.setText(f"共 {total_count} 个已归档工单")

            # 更新表格
            self.archived_table.setRowCount(total_count)

            for row_idx, order in enumerate(archived_orders):
                # 工单号
                work_order_number = order['work_order_number']
                self.archived_table.setItem(row_idx, 0, QTableWidgetItem(str(work_order_number)))

                # A列
                column_a = order['work_order_info']['column_a'] or ""
                self.archived_table.setItem(row_idx, 1, QTableWidgetItem(str(column_a)))

                # C列
                column_c = order['work_order_info']['column_c'] or ""
                self.archived_table.setItem(row_idx, 2, QTableWidgetItem(str(column_c)))

                # 归档时间
                archive_date = order['archive_date']
                if archive_date:
                    # 格式化时间显示
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(archive_date.replace('Z', '+00:00'))
                        formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_date = str(archive_date)
                else:
                    formatted_date = ""
                self.archived_table.setItem(row_idx, 3, QTableWidgetItem(formatted_date))

                # Excel行号
                row_number = order['row_number']
                self.archived_table.setItem(row_idx, 4, QTableWidgetItem(str(row_number)))

                # 状态
                status = order['work_order_info']['column_q'] or "已归档"
                self.archived_table.setItem(row_idx, 5, QTableWidgetItem(str(status)))

                # 存储工单ID用于后续操作
                self.archived_table.item(row_idx, 0).setData(Qt.ItemDataRole.UserRole, order['work_order_id'])
                self.archived_table.item(row_idx, 4).setData(Qt.ItemDataRole.UserRole, order['row_number'])

            print(f"✅ 归档工单数据加载完成，共 {total_count} 条记录")

        except Exception as e:
            print(f"刷新归档数据时出错: {e}")
            QMessageBox.critical(self, "错误", f"刷新归档数据时出错: {e}")
            self.archived_stats_label.setText("数据加载失败")
            self.archived_table.setRowCount(0)

    def show_archived_context_menu(self, position):
        """显示归档工单右键菜单"""
        if self.archived_table.itemAt(position) is None:
            return

        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 3px;
            }
            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)

        # 回退工单
        rollback_action = menu.addAction("↩️ 回退工单")
        rollback_action.triggered.connect(self.rollback_archived_work_order)

        menu.exec(self.archived_table.mapToGlobal(position))

    def rollback_archived_work_order(self):
        """回退已归档工单"""
        current_row = self.archived_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要回退的工单")
            return

        try:
            # 获取工单信息
            work_order_number_item = self.archived_table.item(current_row, 0)
            row_number_item = self.archived_table.item(current_row, 4)

            if not work_order_number_item or not row_number_item:
                QMessageBox.warning(self, "错误", "无法获取工单信息")
                return

            work_order_id = work_order_number_item.data(Qt.ItemDataRole.UserRole)
            work_order_number = work_order_number_item.text()
            excel_row_number = row_number_item.data(Qt.ItemDataRole.UserRole)

            # 确认回退操作
            reply = QMessageBox.question(
                self,
                "确认回退",
                f"确定要回退工单 {work_order_number} (第{excel_row_number}行) 吗？\n\n此操作将：\n"
                "• 清除所有流程处理结果（是/否选择）\n"
                "• 将工单状态恢复为在途\n"
                "• 删除流程进度记录\n"
                "• 从归档列表中移除\n\n"
                "此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 执行回退操作
            success = self.perform_archived_work_order_rollback(work_order_id, excel_row_number)

            if success:
                # 刷新归档数据显示
                self.refresh_archived_data()
                QMessageBox.information(self, "回退成功", f"工单 {work_order_number} 已成功回退到在途状态")
            else:
                QMessageBox.warning(self, "回退失败", "工单回退操作失败，请检查日志")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"回退工单时出错: {str(e)}")

    def perform_archived_work_order_rollback(self, work_order_id, excel_row_number):
        """执行已归档工单的回退操作"""
        try:
            print(f"开始回退已归档工单: work_order_id={work_order_id}, 行号={excel_row_number}")

            # 1. 清除数据库中的流程处理结果（R-AD列）
            success = self.clear_work_order_process_results(work_order_id)
            if not success:
                print("清除数据库流程结果失败")
                return False

            # 2. 删除流程进度记录
            success = self.delete_process_progress(work_order_id)
            if not success:
                print("删除流程进度记录失败")
                return False

            # 3. 更新数据库中的归档状态为在途
            success = self.update_work_order_archive_status(work_order_id, "在途")
            if not success:
                print("更新数据库归档状态失败")
                return False

            # 4. 从归档工单记录表中删除记录
            success = self.data_manager.delete_archived_work_order(work_order_id)
            if not success:
                print("删除归档工单记录失败")
                return False

            # 5. 更新内存中的数据状态为在途（如果数据已加载）
            if hasattr(self, 'current_data') and self.current_data:
                self.update_memory_data_status(excel_row_number, "在途")

            print(f"已归档工单回退完成: work_order_id={work_order_id}")
            return True

        except Exception as e:
            print(f"执行已归档工单回退时出错: {e}")
            return False

    def update_work_order_archive_status(self, work_order_id, status):
        """更新工单数据库中的归档状态"""
        try:
            updates = {"column_q": status}
            success = self.update_work_order_columns(work_order_id, updates)

            if success:
                print(f"成功更新工单 {work_order_id} 的归档状态为: {status}")
            else:
                print(f"更新工单 {work_order_id} 的归档状态失败")

            return success

        except Exception as e:
            print(f"更新工单归档状态时出错: {e}")
            return False

    def get_process_time_status_info(self, process_key):
        """获取流程时间状态信息（颜色和文本）"""
        try:
            # 检查是否已选择流程详情
            if not hasattr(self, 'selected_process_details') or not self.selected_process_details:
                return {
                    'color': 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #667eea, stop:1 #764ba2)',  # 默认蓝色渐变
                    'text': '⏱️ 时间状态：等待流程选择'
                }

            # 获取当前流程的天数限制
            process_days = self.get_process_days_limit(process_key)
            if process_days is None:
                return {
                    'color': 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #6c757d, stop:1 #5a6268)',  # 灰色渐变 - 无时间限制
                    'text': '⏱️ 时间状态：无时间限制'
                }

            # 计算已经过去的天数（从导入数据那天开始）
            elapsed_days = self.calculate_elapsed_days()

            if elapsed_days <= process_days:
                # 在时间范围内 - 绿色渐变背景
                remaining_days = process_days - elapsed_days
                return {
                    'color': 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:1 #20c997)',  # 绿色渐变
                    'text': f'🟢 时间状态：正常 (剩余 {remaining_days} 天，总计 {process_days} 天)'
                }
            else:
                # 超时 - 红色渐变背景
                overtime_days = elapsed_days - process_days
                return {
                    'color': 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #dc3545, stop:1 #c82333)',  # 红色渐变
                    'text': f'🔴 时间状态：超时 (超时 {overtime_days} 天，限制 {process_days} 天)'
                }

        except Exception as e:
            print(f"获取时间状态时出错: {e}")
            return {
                'color': 'qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #6c757d, stop:1 #5a6268)',  # 灰色渐变
                'text': '⚠️ 时间状态：获取失败'
            }

    def get_process_days_limit(self, process_key):
        """获取当前流程的天数限制"""
        try:
            # 检查是否已选择流程详情
            if not hasattr(self, 'selected_process_details') or not self.selected_process_details:
                return None

            # 从选择的流程详情中获取对应流程的天数
            process_name_map = {
                'planning': '规划',
                'construction': '建设',
                'maintenance': '维护',
                'optimization': '优化',
                'customer': '客户'
            }

            target_process_name = process_name_map.get(process_key)
            if not target_process_name:
                return None

            # 在选择的流程详情中查找对应的天数
            for detail in self.selected_process_details:
                if detail.startswith(f"{target_process_name}:"):
                    # 提取天数信息，格式如："建设: 催开已规划待落地--覆盖类小区拉远(覆盖类) (90天)"
                    import re
                    match = re.search(r'\((\d+)天\)', detail)
                    if match:
                        return int(match.group(1))

            return None

        except Exception as e:
            print(f"获取流程天数限制时出错: {e}")
            return None

    def calculate_elapsed_days(self):
        """计算从导入数据到现在经过的天数"""
        try:
            from datetime import datetime, date

            # 尝试从数据管理器获取最新的导入时间
            if hasattr(self, 'data_manager') and self.data_manager:
                try:
                    # 获取最新的上传记录
                    records = self.data_manager.get_upload_records()
                    if records:
                        # 使用最新的上传时间
                        latest_record = records[0]  # 假设records按时间倒序排列
                        import_time_str = latest_record.get('upload_time', '')
                        if import_time_str:
                            import_datetime = datetime.strptime(import_time_str, '%Y-%m-%d %H:%M:%S')
                            import_date = import_datetime.date()
                            current_date = date.today()
                            elapsed_days = (current_date - import_date).days
                            return elapsed_days
                except Exception as e:
                    print(f"从数据库获取导入时间失败: {e}")

            # 如果无法从数据库获取，使用默认值（假设今天导入）
            return 0

        except Exception as e:
            print(f"计算经过天数时出错: {e}")
            return 0

    def show_test_time_edit_page(self):
        """显示修改现场测试时间页面"""
        try:
            # 更新按钮状态
            self.update_sidebar_buttons(self.test_time_edit_btn)

            # 立即切换到修改现场测试时间页面，避免卡顿
            self.stacked_widget.setCurrentWidget(self.test_time_edit_page)

            # 使用QTimer延迟加载数据，避免界面卡顿
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(50, self.refresh_test_time_data_async)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示修改现场测试时间页面时出错: {e}")

    def refresh_test_time_data_async(self):
        """异步刷新现场测试时间数据"""
        # 检查是否需要刷新数据（避免重复加载）
        if hasattr(self, '_test_time_data_loaded') and self._test_time_data_loaded:
            print("📋 测试时间数据已缓存，跳过重复加载")
            return

        # 显示加载提示（简化方式，避免单元格合并问题）
        if hasattr(self, 'test_time_table'):
            self.test_time_table.setRowCount(1)
            # 在第一列显示加载提示
            loading_item = QTableWidgetItem("正在加载数据...")
            loading_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.test_time_table.setItem(0, 0, loading_item)
            # 清空其他列
            for col in range(1, 6):
                self.test_time_table.setItem(0, col, QTableWidgetItem(""))

        # 延迟执行实际的数据加载
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(100, self.refresh_test_time_data)

    def refresh_test_time_data(self):
        """刷新现场测试时间数据"""
        try:
            if not hasattr(self, 'data_manager') or not self.data_manager:
                QMessageBox.warning(self, "提示", "请先导入数据")
                return

            print("🔄 开始加载测试时间数据...")

            # 清除之前的单元格合并状态
            if hasattr(self, 'test_time_table'):
                self.test_time_table.clearSpans()

            # 从数据库获取所有工单数据
            conn = self.data_manager.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, row_number, column_a, column_b, column_c, column_j, updated_at
                FROM work_orders
                WHERE is_valid = 1
                ORDER BY row_number
            ''')

            records = cursor.fetchall()
            conn.close()

            # 更新表格
            self.test_time_table.setRowCount(len(records))

            for row, record in enumerate(records):
                # 设置行高
                self.test_time_table.setRowHeight(row, 40)
                # 行号
                self.test_time_table.setItem(row, 0, QTableWidgetItem(str(record['row_number'])))

                # 工单号 (B列)
                self.test_time_table.setItem(row, 1, QTableWidgetItem(str(record['column_b'] or '')))

                # 地址 (C列)
                self.test_time_table.setItem(row, 2, QTableWidgetItem(str(record['column_c'] or '')))

                # 当前测试时间
                current_time = str(record['column_j'] or '')
                time_item = QTableWidgetItem(current_time)
                self.test_time_table.setItem(row, 3, time_item)

                # 操作按钮 - 创建容器来居中对齐
                button_widget = QWidget()
                button_layout = QHBoxLayout(button_widget)
                button_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
                button_layout.setSpacing(0)  # 移除间距
                button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

                edit_btn = ModernButton("✏️ 修改", True)
                edit_btn.setFixedHeight(26)  # 稍微减小高度
                edit_btn.setFixedWidth(60)   # 增加宽度，让按钮更舒适
                edit_btn.clicked.connect(lambda checked, work_id=record['id'], current_val=current_time:
                                       self.edit_test_time(work_id, current_val))

                button_layout.addWidget(edit_btn)
                self.test_time_table.setCellWidget(row, 4, button_widget)

                # 最后修改时间
                self.test_time_table.setItem(row, 5, QTableWidgetItem(str(record['updated_at'] or '')))

            # 调整列宽
            self.test_time_table.resizeColumnsToContents()

            # 设置操作列的固定宽度，确保按钮完全显示
            self.test_time_table.setColumnWidth(4, 120)  # 操作列设置为120像素宽，给60px按钮充足空间

            # 设置列宽比例
            header = self.test_time_table.horizontalHeader()
            header.setStretchLastSection(True) # type: ignore

            # 标记数据已加载
            self._test_time_data_loaded = True
            print("✅ 测试时间数据加载完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新数据时出错: {e}")
            self._test_time_data_loaded = False

    def reset_test_time_cache(self):
        """重置测试时间数据缓存"""
        self._test_time_data_loaded = False
        print("🔄 重置测试时间数据缓存")

    def filter_test_time_data(self):
        """筛选现场测试时间数据"""
        try:
            search_text = self.test_time_search_input.text().strip().lower()

            for row in range(self.test_time_table.rowCount()):
                should_show = True

                if search_text:
                    # 检查各列是否包含搜索文本
                    row_text = ""
                    for col in range(3):  # 只搜索前3列（行号、工单号、地址）
                        item = self.test_time_table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "

                    should_show = search_text in row_text

                self.test_time_table.setRowHidden(row, not should_show)

        except Exception as e:
            print(f"筛选数据时出错: {e}")

    def edit_test_time(self, work_id, current_value):
        """编辑现场测试时间"""
        try:
            # 弹出输入对话框
            new_value, ok = QInputDialog.getText(
                self,
                "修改现场测试时间",
                f"当前值：{current_value}\n\n请输入新的现场测试时间：",
                text=current_value
            )

            if ok and new_value != current_value:
                # 确认修改
                reply = QMessageBox.question(
                    self,
                    "确认修改",
                    f"确定要将现场测试时间从\n'{current_value}'\n修改为\n'{new_value}'\n吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # 更新数据库
                    conn = self.data_manager.db.get_connection()
                    cursor = conn.cursor()

                    cursor.execute('''
                        UPDATE work_orders
                        SET column_j = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (new_value, work_id))

                    conn.commit()
                    conn.close()

                    # 刷新表格
                    self.refresh_test_time_data()

                    QMessageBox.information(self, "成功", "现场测试时间修改成功！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"修改现场测试时间时出错: {e}")

def signal_handler(signum, _):
    """处理系统信号（如Ctrl+C）"""
    print(f"\n接收到信号 {signum}，正在退出程序...")
    QApplication.quit()
    sys.exit(0)

def main():
    # 设置信号处理器，处理Ctrl+C等信号
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 设置应用程序图标和信息
    app.setApplicationName("基于python的可视化投诉工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Excel Tools")

    # 创建主窗口
    window = ExcelReaderApp()
    window.show()

    # 设置定时器以便能够处理系统信号
    timer = QTimer()
    timer.timeout.connect(lambda: None)  # 空操作，只是为了让事件循环能够处理信号
    timer.start(100)  # 每100ms触发一次

    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n检测到键盘中断，正在退出...")
        sys.exit(0)

if __name__ == "__main__":
    main()