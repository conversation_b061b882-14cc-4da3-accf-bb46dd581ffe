#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单状态隔离功能
验证切换工单时状态是否被正确清理
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_state_isolation():
    """测试工单状态隔离"""
    app = QApplication(sys.argv)
    
    # 创建主应用
    main_app = ExcelReaderApp()
    main_app.show()
    
    def simulate_work_order_switching():
        """模拟工单切换过程"""
        try:
            print("🧪 开始测试工单状态隔离...")
            
            # 检查是否有数据
            if not hasattr(main_app, 'current_data') or not main_app.current_data:
                print("❌ 没有加载数据，无法测试")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 模拟设置一些页面选择状态
            print("📝 模拟设置页面选择状态...")
            main_app.current_page_selections = {
                'planning': {
                    'big_network': '是',
                    'non_big_network': '否'
                },
                'construction': {
                    'property': '是',
                    'transmission': '否'
                }
            }
            print(f"设置的状态: {main_app.current_page_selections}")
            
            # 模拟切换到新工单
            print("🔄 模拟切换到新工单...")
            
            # 找到一个在途工单进行测试
            transit_rows = []
            for i, row in enumerate(main_app.current_data[2:], start=3):
                if len(row) > 16 and str(row[16]).strip() == "在途":  # Q列是归档状态
                    transit_rows.append(i)
                    if len(transit_rows) >= 2:  # 找到至少2个在途工单
                        break
            
            if len(transit_rows) < 2:
                print("❌ 需要至少2个在途工单进行测试")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 模拟选择第一个工单
            first_row = transit_rows[0]
            print(f"选择第一个工单 (行号: {first_row})")
            main_app.current_work_order_id = main_app.get_work_order_id_by_row(first_row)
            print(f"第一个工单ID: {main_app.current_work_order_id}")
            
            # 检查状态是否还在
            print(f"切换前的状态: {getattr(main_app, 'current_page_selections', {})}")
            
            # 模拟切换到第二个工单
            second_row = transit_rows[1]
            print(f"切换到第二个工单 (行号: {second_row})")
            
            # 模拟调用工单切换逻辑
            if hasattr(main_app, 'handle_work_order_selection'):
                # 构造模拟的行数据
                row_data = main_app.current_data[second_row - 1] if second_row - 1 < len(main_app.current_data) else []
                main_app.handle_work_order_selection(row_data)
            
            # 检查状态是否被清理
            final_state = getattr(main_app, 'current_page_selections', {})
            print(f"切换后的状态: {final_state}")
            
            # 验证结果
            if not final_state or final_state == {}:
                print("✅ 状态隔离测试通过：页面选择状态已被正确清理")
            else:
                print("❌ 状态隔离测试失败：页面选择状态未被清理")
                print(f"残留状态: {final_state}")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 退出应用
        QTimer.singleShot(1000, app.quit)
    
    # 延迟执行测试，确保界面完全加载
    QTimer.singleShot(2000, simulate_work_order_switching)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    print("🧪 开始测试工单状态隔离功能...")
    test_state_isolation()
