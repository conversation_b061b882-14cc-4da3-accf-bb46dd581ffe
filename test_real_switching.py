#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的工单切换功能
模拟用户双击工单行的操作
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QTableWidgetItem
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_real_switching():
    """测试真实的工单切换功能"""
    app = QApplication(sys.argv)
    
    # 创建主应用
    main_app = ExcelReaderApp()
    main_app.show()
    
    def test_switching_logic():
        """测试切换逻辑"""
        try:
            print("🧪 开始测试真实工单切换...")
            
            # 检查是否有数据
            if not hasattr(main_app, 'current_data') or not main_app.current_data:
                print("❌ 没有加载数据，无法测试")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 检查在途数据表格是否存在
            if not hasattr(main_app, 'transit_table'):
                print("❌ 在途数据表格不存在")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 检查表格是否有数据
            if main_app.transit_table.rowCount() < 2:
                print("❌ 在途数据表格中没有足够的数据进行测试")
                QTimer.singleShot(1000, app.quit)
                return
            
            print(f"✅ 在途数据表格有 {main_app.transit_table.rowCount()} 行数据")
            
            # 1. 设置初始状态
            print("📝 设置初始页面选择状态...")
            main_app.current_page_selections = {
                'planning': {
                    'big_network': '是',
                    'non_big_network': '否'
                },
                'construction': {
                    'property': '是',
                    'transmission': '否'
                }
            }
            main_app.current_work_order_id = 99999  # 设置一个假的工单ID
            
            print(f"初始状态:")
            print(f"  current_page_selections: {main_app.current_page_selections}")
            print(f"  current_work_order_id: {main_app.current_work_order_id}")
            
            # 2. 模拟双击第一行
            print("\n🖱️ 模拟双击第一行工单...")
            first_item = main_app.transit_table.item(0, 0)  # 第一行第一列
            if first_item:
                print(f"第一行数据: {first_item.text()}")
                
                # 调用真实的双击处理方法
                main_app.show_transit_detail_page(first_item)
                
                print(f"双击第一行后:")
                print(f"  current_page_selections: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
                print(f"  current_work_order_id: {getattr(main_app, 'current_work_order_id', 'NOT_SET')}")
                
                # 验证状态是否被清理
                if getattr(main_app, 'current_page_selections', None) == {}:
                    print("✅ 第一次切换：状态清理正常")
                else:
                    print("❌ 第一次切换：状态清理失败")
                    print(f"残留状态: {getattr(main_app, 'current_page_selections', {})}")
            
            # 3. 重新设置状态，然后切换到第二行
            print("\n📝 重新设置状态...")
            main_app.current_page_selections = {
                'maintenance': {
                    'sporadic': '是',
                    'outsourced': '否'
                }
            }
            
            print(f"重新设置后:")
            print(f"  current_page_selections: {main_app.current_page_selections}")
            
            # 4. 模拟双击第二行
            if main_app.transit_table.rowCount() > 1:
                print("\n🖱️ 模拟双击第二行工单...")
                second_item = main_app.transit_table.item(1, 0)  # 第二行第一列
                if second_item:
                    print(f"第二行数据: {second_item.text()}")
                    
                    # 调用真实的双击处理方法
                    main_app.show_transit_detail_page(second_item)
                    
                    print(f"双击第二行后:")
                    print(f"  current_page_selections: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
                    print(f"  current_work_order_id: {getattr(main_app, 'current_work_order_id', 'NOT_SET')}")
                    
                    # 验证状态是否被清理
                    if getattr(main_app, 'current_page_selections', None) == {}:
                        print("✅ 第二次切换：状态清理正常")
                    else:
                        print("❌ 第二次切换：状态清理失败")
                        print(f"残留状态: {getattr(main_app, 'current_page_selections', {})}")
            
            # 5. 测试同一工单的情况
            print("\n🔄 测试双击同一工单...")
            main_app.current_page_selections = {
                'optimization': {
                    'antenna': '是',
                    'backend': '否'
                }
            }
            
            print(f"再次设置状态:")
            print(f"  current_page_selections: {main_app.current_page_selections}")
            
            # 再次双击第二行（同一工单）
            if main_app.transit_table.rowCount() > 1:
                second_item = main_app.transit_table.item(1, 0)
                if second_item:
                    print(f"再次双击第二行: {second_item.text()}")
                    
                    main_app.show_transit_detail_page(second_item)
                    
                    print(f"再次双击同一工单后:")
                    print(f"  current_page_selections: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
                    
                    # 对于同一工单，状态应该保持（不被清理）
                    current_selections = getattr(main_app, 'current_page_selections', {})
                    if current_selections and 'optimization' in current_selections:
                        print("✅ 同一工单：状态保持正常")
                    else:
                        print("⚠️ 同一工单：状态被意外清理")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 退出应用
        QTimer.singleShot(2000, app.quit)
    
    # 延迟执行测试，确保界面完全加载
    QTimer.singleShot(3000, test_switching_logic)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    print("🧪 开始测试真实工单切换功能...")
    test_real_switching()
