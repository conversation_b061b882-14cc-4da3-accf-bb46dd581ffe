#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI状态缓存问题：模拟用户实际操作流程页面
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_ui_state_caching():
    """测试UI状态缓存问题"""
    print("🧪 开始测试UI状态缓存问题...")
    
    app = QApplication(sys.argv)
    main_app = ExcelReaderApp()
    
    # 模拟加载数据
    print("📁 模拟加载Excel数据...")
    
    # 创建模拟数据
    mock_data = [
        ["行号", "工单编号", "类型", "描述", "号码", "原因", "状态"],  # 表头1
        ["", "", "", "", "", "", ""],  # 表头2
        [3, "WO001", "投诉", "网络问题", "13800138001", "信号差", "在途"],  # 工单1
        [4, "WO002", "投诉", "通话问题", "13800138002", "掉话", "在途"],  # 工单2
    ]
    
    main_app.current_data = mock_data
    main_app.initialize_transit_preview()
    
    print("✅ 数据加载完成")
    
    # 测试场景：模拟用户实际操作
    def test_scenario():
        try:
            print("\n🎯 测试场景：模拟用户实际操作流程页面")
            
            # 步骤1：选择第一个工单并开始处理
            print("\n1️⃣ 步骤1：选择第一个工单并开始处理")
            first_row_data = mock_data[2]
            excel_row_number = int(first_row_data[0])
            work_order_id_1 = main_app.get_work_order_id_by_row(excel_row_number)
            
            # 设置工单状态
            main_app.current_work_order_id = work_order_id_1
            main_app.current_work_order_data = first_row_data
            print(f"   设置当前工单ID: {work_order_id_1}")
            
            # 模拟用户选择流程
            main_app.selected_processes = ['规划']
            main_app.selected_process_details = ['规划:大网']
            main_app.current_process_index = 0
            main_app.process_results = {}
            
            # 保存流程进度
            main_app.data_manager.save_process_progress(
                work_order_id_1, 
                main_app.selected_processes,
                main_app.selected_process_details,
                main_app.current_process_index,
                main_app.process_results
            )
            
            # 模拟进入规划流程页面
            print("   模拟进入规划流程页面...")
            main_app.show_current_process_page()
            
            # 等待页面加载完成
            def step1_continue():
                print("   检查规划页面的初始状态...")
                
                # 检查页面是否正确显示
                if 'planning' in main_app.process_pages:
                    planning_page = main_app.process_pages['planning']
                    print(f"   ✅ 规划页面已创建")
                    
                    # 检查按钮组状态
                    from PyQt6.QtWidgets import QButtonGroup
                    button_groups = planning_page.findChildren(QButtonGroup)
                    print(f"   找到 {len(button_groups)} 个按钮组")
                    
                    for group in button_groups:
                        item_key = group.property("item_key")
                        checked_button = group.checkedButton()
                        if checked_button:
                            checked_id = group.checkedId()
                            value = "是" if checked_id == 1 else "否"
                            print(f"   ❌ 发现预选状态: {item_key} = {value}")
                        else:
                            print(f"   ✅ {item_key} 没有预选状态")
                
                # 模拟用户选择"大网=是"
                print("   模拟用户选择'大网=是'...")
                main_app.current_page_selections = {'big_network': '是'}
                
                # 更新数据库
                updates = {'planning_big_network': '是'}
                main_app.update_work_order_columns(work_order_id_1, updates)
                main_app.data_manager.update_process_progress(
                    work_order_id_1,
                    main_app.current_process_index,
                    main_app.process_results,
                    main_app.current_page_selections
                )
                print("   ✅ 已保存选择到数据库")
                
                # 继续下一步
                QTimer.singleShot(500, step2_start)
            
            def step2_start():
                print("\n2️⃣ 步骤2：切换到第二个工单（未处理的）")
                second_row_data = mock_data[3]
                excel_row_number_2 = int(second_row_data[0])
                work_order_id_2 = main_app.get_work_order_id_by_row(excel_row_number_2)
                
                # 清理状态并切换工单
                main_app.clear_previous_work_order_state()
                main_app.current_page_selections = {}
                main_app.clear_all_process_page_selections()
                
                main_app.current_work_order_id = work_order_id_2
                main_app.current_work_order_data = second_row_data
                print(f"   切换到工单ID: {work_order_id_2}")
                
                # 模拟用户选择流程（但不开始处理）
                print("   模拟用户查看第二个工单的流程选择...")
                
                # 检查第二个工单是否有预填状态
                planning_selections = main_app.load_page_selections_from_database('planning')
                print(f"   第二个工单的规划选择状态: {planning_selections}")
                
                if planning_selections:
                    print("   ❌ 错误：第二个工单显示了第一个工单的选择状态！")
                else:
                    print("   ✅ 正确：第二个工单没有显示之前的选择状态")
                
                # 模拟用户进入规划页面查看
                print("   模拟用户进入第二个工单的规划页面...")
                
                # 设置流程状态（但不保存到数据库）
                main_app.selected_processes = ['规划']
                main_app.selected_process_details = ['规划:大网']
                main_app.current_process_index = 0
                main_app.process_results = {}
                
                # 进入页面
                main_app.show_current_process_page()
                
                # 等待页面加载完成
                QTimer.singleShot(500, step2_check)
            
            def step2_check():
                print("   检查第二个工单规划页面的状态...")
                
                if 'planning' in main_app.process_pages:
                    planning_page = main_app.process_pages['planning']
                    
                    # 检查按钮组状态
                    from PyQt6.QtWidgets import QButtonGroup
                    button_groups = planning_page.findChildren(QButtonGroup)
                    
                    has_preselected = False
                    for group in button_groups:
                        item_key = group.property("item_key")
                        checked_button = group.checkedButton()
                        if checked_button:
                            checked_id = group.checkedId()
                            value = "是" if checked_id == 1 else "否"
                            print(f"   ❌ 第二个工单发现预选状态: {item_key} = {value}")
                            has_preselected = True
                        else:
                            print(f"   ✅ 第二个工单 {item_key} 没有预选状态")
                    
                    if not has_preselected:
                        print("   ✅ 第二个工单的规划页面完全空白，修复成功！")
                    else:
                        print("   ❌ 第二个工单的规划页面有预选状态，仍然存在bug！")
                
                print("\n🎉 测试完成！")
                
                # 退出应用
                QTimer.singleShot(1000, app.quit)
            
            # 开始测试
            QTimer.singleShot(500, step1_continue)
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
            app.quit()
    
    # 延迟执行测试，确保界面完全初始化
    QTimer.singleShot(500, test_scenario)
    
    # 显示主窗口
    main_app.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_ui_state_caching()
