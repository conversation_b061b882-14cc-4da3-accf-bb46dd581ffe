#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流程处理功能
模拟用户的实际使用场景：
1. 选择一个工单进行流程处理
2. 在流程页面做选择
3. 切换到另一个未开始的工单
4. 验证新工单的页面是否为空白状态
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import ExcelReaderApp

def test_process_flow():
    """测试流程处理功能"""
    app = QApplication(sys.argv)
    
    # 创建主应用
    main_app = ExcelReaderApp()
    main_app.show()
    
    def test_flow_logic():
        """测试流程逻辑"""
        try:
            print("🧪 开始测试流程处理功能...")
            
            # 检查是否有数据
            if not hasattr(main_app, 'current_data') or not main_app.current_data:
                print("❌ 没有加载数据，无法测试")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 检查在途数据表格是否存在
            if not hasattr(main_app, 'transit_table'):
                print("❌ 在途数据表格不存在")
                QTimer.singleShot(1000, app.quit)
                return
            
            # 检查表格是否有数据
            if main_app.transit_table.rowCount() < 2:
                print("❌ 在途数据表格中没有足够的数据进行测试")
                QTimer.singleShot(1000, app.quit)
                return
            
            print(f"✅ 在途数据表格有 {main_app.transit_table.rowCount()} 行数据")
            
            # 步骤1：选择第一个工单并开始流程处理
            print("\n📋 步骤1：选择第一个工单并开始流程处理...")
            first_item = main_app.transit_table.item(0, 0)
            if first_item:
                print(f"选择工单: {first_item.text()}")
                
                # 双击进入工单详情
                main_app.show_transit_detail_page(first_item)
                
                # 模拟选择流程
                print("🔧 模拟选择流程...")
                if hasattr(main_app, 'process_checkboxes'):
                    # 选择规划流程
                    if 'planning' in main_app.process_checkboxes:
                        main_app.process_checkboxes['planning'].setChecked(True)
                        print("✅ 选择了规划流程")
                    
                    # 选择建设流程
                    if 'construction' in main_app.process_checkboxes:
                        main_app.process_checkboxes['construction'].setChecked(True)
                        print("✅ 选择了建设流程")
                
                # 模拟点击开始处理
                print("🚀 模拟开始流程处理...")
                try:
                    main_app.start_process_handling()
                    print("✅ 流程处理已开始")
                except Exception as e:
                    print(f"⚠️ 开始流程处理时出错: {e}")
                
                # 检查当前状态
                print(f"当前工单ID: {getattr(main_app, 'current_work_order_id', 'NOT_SET')}")
                print(f"当前页面选择: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
                
                # 模拟在流程页面做选择
                print("📝 模拟在流程页面做选择...")
                main_app.current_page_selections = {
                    'planning': {
                        'big_network': '是',
                        'non_big_network': '否'
                    }
                }
                print(f"设置页面选择: {main_app.current_page_selections}")
            
            # 步骤2：切换到第二个工单（未开始的）
            print("\n📋 步骤2：切换到第二个工单（未开始的）...")
            if main_app.transit_table.rowCount() > 1:
                second_item = main_app.transit_table.item(1, 0)
                if second_item:
                    print(f"切换到工单: {second_item.text()}")
                    
                    # 双击进入第二个工单
                    main_app.show_transit_detail_page(second_item)
                    
                    # 检查状态是否被清理
                    print(f"切换后工单ID: {getattr(main_app, 'current_work_order_id', 'NOT_SET')}")
                    print(f"切换后页面选择: {getattr(main_app, 'current_page_selections', 'NOT_SET')}")
                    
                    # 验证状态清理
                    current_selections = getattr(main_app, 'current_page_selections', {})
                    if not current_selections or current_selections == {}:
                        print("✅ 状态清理成功：新工单页面选择为空")
                    else:
                        print(f"❌ 状态清理失败：新工单仍有页面选择 {current_selections}")
                    
                    # 模拟选择流程（测试是否会开始流程处理）
                    print("🔧 在新工单上模拟选择流程...")
                    if hasattr(main_app, 'process_checkboxes'):
                        # 选择维护流程
                        if 'maintenance' in main_app.process_checkboxes:
                            main_app.process_checkboxes['maintenance'].setChecked(True)
                            print("✅ 在新工单上选择了维护流程")
                        
                        # 模拟开始处理
                        try:
                            main_app.start_process_handling()
                            print("✅ 新工单流程处理已开始")
                            
                            # 检查是否有流程页面
                            if hasattr(main_app, 'process_pages') and 'maintenance' in main_app.process_pages:
                                print("✅ 维护流程页面已创建")
                                
                                # 检查页面是否为空白状态
                                maintenance_page = main_app.process_pages['maintenance']
                                print("🔍 检查维护流程页面状态...")
                                
                                # 这里应该检查页面上的单选按钮是否都未选中
                                # 由于页面结构复杂，我们主要检查current_page_selections
                                current_selections = getattr(main_app, 'current_page_selections', {})
                                maintenance_selections = current_selections.get('maintenance', {})
                                
                                if not maintenance_selections:
                                    print("✅ 维护流程页面状态为空白，没有预填选择")
                                else:
                                    print(f"❌ 维护流程页面被预填了选择: {maintenance_selections}")
                            
                        except Exception as e:
                            print(f"⚠️ 新工单开始流程处理时出错: {e}")
            
            # 步骤3：总结测试结果
            print("\n📊 测试结果总结:")
            print("1. 工单切换时状态清理是否正常")
            print("2. 新工单的流程页面是否为空白状态")
            print("3. 是否还存在状态污染问题")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 退出应用
        QTimer.singleShot(3000, app.quit)
    
    # 延迟执行测试，确保界面完全加载
    QTimer.singleShot(3000, test_flow_logic)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    print("🧪 开始测试流程处理功能...")
    test_process_flow()
